/**
 * 将HEX颜色代码转换为RGBA字符串
 * @param hex HEX颜色代码（支持3位或6位，可包含#前缀）
 * @param alpha 透明度（0-1），默认1
 * @returns RGBA颜色字符串
 * @throws 当传入无效的HEX格式时抛出错误
 */
export function hexToRgba(hex: string, alpha: number = 1): string {
  // 如果是rgba格式，返回
  if (hex.startsWith('rgba')) {
    return hex
  }

  // 验证alpha值范围
  if (alpha < 0 || alpha > 1) {
    throw new Error('Alpha值必须在0到1之间')
  }

  // 移除#号并统一转为大写
  let cleanHex = hex.replace(/^#/, '').toUpperCase()

  // 验证是否为有效HEX格式
  if (!/^([0-9A-F]{3}){1,2}$/i.test(cleanHex)) {
    throw new Error('无效的HEX颜色格式')
  }

  // 处理缩写格式（3位转6位）
  if (cleanHex.length === 3) {
    cleanHex = cleanHex
      .split('')
      .map((char) => char + char)
      .join('')
  }

  // 解析RGB值
  const r = parseInt(cleanHex.substring(0, 2), 16)
  const g = parseInt(cleanHex.substring(2, 4), 16)
  const b = parseInt(cleanHex.substring(4, 6), 16)

  // 返回RGBA字符串
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

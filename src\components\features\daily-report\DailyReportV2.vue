<template>
  <SearchForm showReset @search="handleSearch" @reset="handleReset">
    <template #form-fields>
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-300px">
        <n-date-picker
          v-model:value="time"
          class="w-full"
          size="large"
          placeholder="请选择时间"
          :is-date-disabled="disablePreviousDate"
        >
        </n-date-picker>
      </InputGroup>
    </template>

    <template #action-buttons>
      <n-button size="large" @click="handleExport">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出日报
      </n-button>
    </template>
  </SearchForm>
  <div class="px-7.5">
    <div class="py-5">
      <div class="text-xl text-#5F6673 font-bold">新能源市场执行调峰情况</div>
      <span class="text-base text-#A1A9B7">{{ description }} </span>
    </div>

    <div class="flex">
      <div class="w-[calc(100%-480px)] flex-shrink flex-grow">
        <LineChart
          ref="statisticChartRef"
          title="新能源弃电情况统计"
          :data="statisticChartData"
          height="300px"
          :customConfig
        />
        <LineChart
          ref="windChartRef"
          title="风力发电"
          :data="windChartData"
          height="300px"
          :customConfig
        />
        <LineChart
          ref="solarChartRef"
          title="光伏发电"
          :data="solarChartData"
          height="300px"
          :customConfig
        />
      </div>
      <div class="mt-1 w-120">
        <div class="flex justify-between">
          <div class="text-#5F6673 text-xl font-bold mb-2.5">调峰时段分析</div>
          <!-- 偏置前 偏置后选项 -->
          <NSelect
            v-model:value="useStatus"
            :options="useStatusOptions"
            class="w-30"
            @update:value="handleUseStatusChange"
          ></NSelect>
        </div>
        <DataTable height="820px" :columns="peakingPeriodColumns" :data="peakingPeriodData" />
      </div>
    </div>

    <div class="text-#5F6673 text-xl font-bold mb-2.5">风电厂站调峰明细</div>
    <DataTable
      :columns="windPVDetailColumns"
      :data="windGeneratorDetailList"
      height="500px"
      @sort="handleWindSort"
    />

    <div class="text-#5F6673 text-xl font-bold my-2.5">光伏厂站调峰明细</div>
    <DataTable
      :columns="windPVDetailColumns"
      :data="pvGeneratorDetailList"
      height="500px"
      class="mb-2.5"
      @sort="handlePVSort"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue'

import { NButton, NIcon, NDatePicker, NSelect } from 'naive-ui'
import { ExportIcon } from '@/utils/constant/icons'
import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import { disablePreviousDate, formatTime } from '@/utils/tools'
import SearchForm from '@/components/shared/SearchForm.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import { useExportDailyReportV2, useDailyReportV2, useDailyReport } from './hooks'
import { applySorting } from '@/utils/sort'

const statisticChartRef = useTemplateRef('statisticChartRef')
const windChartRef = useTemplateRef('windChartRef')
const solarChartRef = useTemplateRef('solarChartRef')

const {
  time,
  useStatus,
  customConfig,
  windPVDetailColumns,
  windPVDetailExportColumns,
  windGeneratorDetailList,
  pvGeneratorDetailList,
  peakingPeriodColumns,
  peakingPeriodData,
  windChartData,
  statisticChartData,
  solarChartData,
  description,
  fetchDailyReportData,
} = useDailyReportV2()

const { exportWord } = useExportDailyReportV2()

const useStatusOptions = [
  {
    label: '偏置前',
    value: 2,
  },
  {
    label: '偏置后',
    value: 3,
  },
]

const handleUseStatusChange = () => {
  fetchDailyReportData()
}

// 2025-07-09
const handleSearch = () => {
  fetchDailyReportData()
}

const formatTableData = (columns: any[], data: any[]) => {
  const columnsRes = columns.map((item) => item.title)

  const dataRes = data.map((item) => columns.map((column) => item[column.key]))
  return [columnsRes, ...dataRes]
}

const handleWindSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  windGeneratorDetailList.value = applySorting(
    windGeneratorDetailList.value,
    multiSortState,
    windPVDetailColumns,
  )
}

const handlePVSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  pvGeneratorDetailList.value = applySorting(
    pvGeneratorDetailList.value,
    multiSortState,
    windPVDetailColumns,
  )
}

const getDaliyReportV1Data = async () => {
  const { fetchDailyReportData, description, peakShavingData } = useDailyReport()
  await fetchDailyReportData(formatTime(time.value, 'YYYY-MM-DD'))

  return {
    description,
    peakShavingData,
  }
}
/**
 * 导出日报
 */
const handleExport = async () => {
  const statisticChartImage = statisticChartRef.value?.getImg()
  const windChartImage = windChartRef.value?.getImg()
  const solarChartImage = solarChartRef.value?.getImg()
  const peakShavingTableData = formatTableData(
    peakingPeriodColumns.concat({ key: 'reason', title: '调峰原因分析', width: '30%' }),
    peakingPeriodData.value,
  )

  const windGeneratorDetailTableData = formatTableData(
    windPVDetailExportColumns,
    windGeneratorDetailList.value,
  )
  const pvGeneratorDetailTableData = formatTableData(
    windPVDetailExportColumns,
    pvGeneratorDetailList.value,
  )

  const chartTime = formatTime(time.value, 'M月DD日')

  const daliyReportV1Data = await getDaliyReportV1Data()

  const title = `新能源市场执行调峰日报（${useStatus.value === 2 ? '偏置前' : '偏置后'}）`
  exportWord({
    fileName: `${title} ${formatTime(time.value, 'YYYY年MM月DD日')}`,
    title,
    statisticChartImage,
    windChartImage,
    solarChartImage,
    chartTime,
    peakShavingData: peakShavingTableData,
    windGeneratorDetailTableData,
    pvGeneratorDetailTableData,
    description: description.value + '参与调峰时段分析如下：',
    daliyReportV1Data: {
      description: daliyReportV1Data.description.value + '参与调峰时段分析如下：',
      peakShavingData: formatTableData(
        peakingPeriodColumns.concat({ key: 'reason', title: '调峰原因分析', width: '30%' }),
        daliyReportV1Data.peakShavingData.value,
      ),
    },
  })
}

const handleReset = () => {
  time.value = 0
  handleSearch()
}

onMounted(() => {
  handleSearch()
})
</script>

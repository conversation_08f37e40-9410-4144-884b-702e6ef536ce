import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import { visualizer } from 'rollup-plugin-visualizer'
import vue from '@vitejs/plugin-vue'
import VueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from 'unocss/vite'
import svgLoader from 'vite-svg-loader'
import legacy from '@vitejs/plugin-legacy'

// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    VueDevTools(),
    vue(),
    UnoCSS(),
    svgLoader(),
    legacy(),
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      '/dwyzt': {
        target: 'http://*************:8087/mock/189',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyzt/, '/dwyzt'),
      },
    },
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        // 入口文件：放到 js/
        entryFileNames: 'js/[name].[hash].js',
        // 代码分块：也放到 js/
        chunkFileNames: 'js/[name].[hash].js',
        // 静态资源，根据扩展名分流 默认
        // assetFileNames: 'assets/[name]-[hash][extname]',
        advancedChunks: {
          groups: [
            {
              name: 'echarts',
              // 匹配 node_modules 下的 echarts 及其子包
              test: /[\\/]node_modules[\\/]echarts[\\/]/,
            },
            {
              name: 'docx',
              test: /[\\/]node_modules[\\/]docx[\\/]/,
            },
            {
              name: 'exceljs',
              test: /[\\/]node_modules[\\/]exceljs[\\/]/,
            },
          ],
        },
      },
    },
  },
})

<template>
  <div class="flex flex-col h-full">
    <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
      <div class="flex flex-1 text-2xl text-#9E9E9E">{{ timeRange }}</div>
      <div class="flex">
        <ExportButton
          class="mr-2.5"
          filename="全省母线负荷数据详情"
          export-type="excel-structured"
          @export-start="handleExport"
          :structured-data="dataByExport"
        />
        <n-button type="info" size="large" @click="handleBack">
          <template #icon>
            <n-icon><ChevronLeft20FilledIcon /></n-icon>
          </template>
          返回
        </n-button>
      </div>
    </div>

    <LineChart ref="chartRef" :data="chartData" height="450px" class="mt-2" yAxisName="万千瓦" />

    <n-select
      :default-value="selectedSortOption"
      class="w-42.5 self-end mb-2 mr-3"
      :options="SortOptions"
      @update:value="handleSortOptionChange"
    ></n-select>
    <n-scrollbar style="height: calc(100vh - 580px)">
      <div
        v-for="(item, index) in detailData.busLfStatisticList"
        :key="index"
        class="flex flex-col pl-4 mb-5"
      >
        <div class="flex">
          <LoadDetailCard class="w-50" :data="item" />
          <div class="flex flex-col px-5">
            <!-- 横向表格数据 -->
            <VerticalTable
              class="flex-1"
              width="calc(100vw - 755px)"
              :columns="tableColumns"
              :data="item.busLfDataList"
            ></VerticalTable>
          </div>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, useTemplateRef } from 'vue'

import { ChevronLeft20FilledIcon } from '@/utils/constant/icons'
import { NButton, NSelect, NIcon, NScrollbar } from 'naive-ui'

import LineChart from '@/components/shared/charts/LineChart.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import LoadDetailCard from '@/components/features/load-forecasting/common/LoadDetailCard.vue'

import { useProvinceDetailData, useLoadDetailExport } from '../../hooks'
import type { ExportBlockOptions } from '@/utils/export'
import { SortOptions } from '@/utils/constant/options'

const { getExportData } = useLoadDetailExport()
const {
  detailData,
  tableColumns,
  chartData,
  timeRange,
  selectedSortOption,
  handleSortOptionChange,
  handleBack,
} = useProvinceDetailData()
const chartRef = useTemplateRef('chartRef')
const dataByExport = ref<ExportBlockOptions>()

const handleExport = () => {
  dataByExport.value = getExportData(detailData.value, chartRef.value)
}
</script>

<style></style>

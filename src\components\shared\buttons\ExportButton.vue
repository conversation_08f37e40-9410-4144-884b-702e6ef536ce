<template>
  <n-button
    :size="size"
    :class="buttonClass"
    @click="handleExport"
    :loading="loading"
    :disabled="disabled"
  >
    <template #icon>
      <n-icon><ExportIcon /></n-icon>
    </template>
    {{ text }}
  </n-button>
</template>

<script setup lang="ts">
import { NButton, NIcon, useMessage } from 'naive-ui'
import { ExportIcon } from '@/utils/constant/icons'
import {
  exportStructuredExcel,
  exportToExcel,
  exportToWord,
  type ExportBlockOptions,
  type ExportColumn,
} from '@/utils/export'
import type { WordContentItem } from '@/types'
import { nextTick, ref } from 'vue'

// 定义 props
interface Props {
  text?: string
  size?: 'small' | 'medium' | 'large'
  buttonClass?: string
  disabled?: boolean
  data?: any[]
  columns?: any[]
  filename?: string
  exportType?: 'excel' | 'excel-structured' | 'csv' | 'json' | 'word'
  wordContent?: WordContentItem[]
  structuredData?: ExportBlockOptions
}

const props = withDefaults(defineProps<Props>(), {
  text: '导出表格',
  size: 'large',
  buttonClass: '',
  disabled: false,
  data: () => [],
  columns: () => [],
  filename: 'export_data',
  exportType: 'excel',
  structuredData: () => ({
    list: [],
    image: '',
  }),
})

// 定义 emits
const emit = defineEmits<{
  export: [data: any[], columns: any[], filename: string]
  'export-start': []
  'export-success': [filename: string]
  'export-error': [error: Error]
}>()

const message = useMessage()

const loading = ref(false)

// 导出处理函数
const handleExport = async () => {
  loading.value = true

  try {
    emit('export-start')

    await nextTick() // 等待响应式更新，确保子组件能拿到新值

    // 这里可以添加具体的导出逻辑
    await performExport()

    emit('export-success', props.filename)
    loading.value = false
    message.success('导出成功')
  } catch (error) {
    loading.value = false
    console.error('导出失败:', error)
    emit('export-error', error as Error)
    message.error('导出失败，请重试')
  }
}

// 执行导出逻辑
const performExport = async () => {
  // 转换列配置格式
  const exportColumns: ExportColumn[] = props.columns.map((col) => ({
    key: col.key,
    title: col.title,
    width: col.width ? parseInt(col.width) : undefined,
  }))

  // 根据导出类型调用不同的导出函数
  switch (props.exportType) {
    case 'word':
      if (props.wordContent && props.wordContent.length > 0) {
        await exportToWord(props.filename || '导出文档.docx', props.wordContent)
      } else {
        throw new Error('Word 导出需要提供 wordContent 数据')
      }
      break
    case 'excel-structured':
      // TODO: 实现结构化导出
      exportStructuredExcel(props.filename, props.structuredData)
      break
    case 'excel':
    default:
      // 使用导出工具进行导出
      exportToExcel({
        data: props.data,
        columns: exportColumns,
        filename: props.filename,
      })
  }
}
</script>

<style scoped></style>

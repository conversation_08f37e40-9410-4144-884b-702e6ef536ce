import { getTodayTimeRange } from '@/utils/tools'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUnifiedLoadStore = defineStore('busbarLoad', () => {
  const timestampRange = getTodayTimeRange()
  const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

  const isUnifiedLoadDetailVisible = ref<boolean>(false)

  return {
    isUnifiedLoadDetailVisible,
    timeRange,
  }
})

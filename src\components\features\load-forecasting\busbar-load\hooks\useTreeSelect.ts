import type { TreeSelectOption } from 'naive-ui'
import { useRouter, useRoute } from 'vue-router'

export const useTreeSelect = () => {
  const router = useRouter()
  const route = useRoute()

  const handleStationDeviceChange = (value: string, item: TreeSelectOption) => {
    // 根据当前路由确定目标路径
    const targetPath =
      route.name === 'busbar-load-plan'
        ? '/load-forecasting/busbar-load-plan'
        : '/load-forecasting/busbar-load-spot'

    if (item.deviceId) {
      router.push({
        path: targetPath,
        query: {
          key: item.key as string,
          regionCode: item.regionCode as string,
          deviceId: item.key as string,
          name: item.name as string,
          type: 'device',
        },
      })
    } else if (item.stationId) {
      router.push({
        path: targetPath,
        query: {
          key: item.key as string,
          regionCode: item.regionCode as string,
          stationId: item.key as string,
          name: item.name as string,
          type: 'station',
        },
      })
    }
  }

  return {
    handleStationDeviceChange,
  }
}

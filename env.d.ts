/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

import type { MessageApiInjection } from 'naive-ui'

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_UPLOAD_URL: string
  readonly VITE_WEBSOCKET_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare global {
  interface Window {
    $message: MessageApiInjection
    WisActionLoader?: {
      executeCommand(resourceId: string, commandName: string): void
    }
  }
}

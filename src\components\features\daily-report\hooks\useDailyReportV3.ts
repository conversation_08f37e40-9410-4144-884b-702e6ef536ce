import { ref } from 'vue'
import { useCommonStore } from '@/stores'
import type { SeriesData } from '@/types'
import {
  DailyReportService,
  type newEnergyDataItemV2,
  type PeakingPeriodItem,
  type WindPVGeneratorDetailItem,
} from '@/utils/api/services/dailyReport'
import dayjs from 'dayjs'
import { formatTime } from '@/utils/tools'

export const useDailyReportV3 = () => {
  // V3版本在V2版本的基础上增加了一条市场出清曲线
  const chartData: {
    name: string
    data: CharacterData[]
    color: string
    keys: (keyof newEnergyDataItemV2)[]
  }[] = [
    {
      name: '实际出力',
      data: [],
      color: '#5E42CE',
      keys: ['totalPower', 'windPower', 'pvPower'],
    },
    {
      name: '超短期新能源预测',
      data: [],
      color: '#EDAC00',
      keys: ['totalPrediction', 'windPrediction', 'pvPrediction'],
    },
    {
      name: '偏置前出清',
      data: [],
      color: '#42CE83',
      keys: ['totalBeforePower', 'windBeforePower', 'pvBeforePower'],
    },
    {
      name: '偏置后出清',
      data: [],
      color: '#FF4C1E',
      keys: ['totalAfterPower', 'windAfterPower', 'pvAfterPower'],
    },
    {
      name: '市场出清',
      data: [],
      color: '#5E42CE', // 与实际出力同色，或可选不同颜色
      keys: ['totalMarketstring', 'windMarketstring', 'pvMarketstring'], // V3版新增的市场出清数据字段
    },
  ]

  const customConfig = {
    title: {
      left: -5,
      top: 7,
    },
    grid: {
      top: '25%',
    },
  }

  //调峰时段分析column 新能源类型  开始时间  结束时间 最大调峰电力
  const peakingPeriodColumns = [
    {
      key: 'type',
      title: '新能源类型',
      width: '21.5%',
    },
    {
      key: 'startTime',
      title: '开始时间',
      width: '18%',
    },
    {
      key: 'endTime',
      title: '结束时间',
      width: '18%',
    },
    {
      key: 'maxValue',
      title: '最大调峰电力（万千瓦）',
    },
  ]

  const windPVDetailColumns: {
    key: keyof WindPVGeneratorDetailItem
    title: string
    width?: string
    sorter?: any
  }[] = [
    {
      key: 'generatorName',
      title: '模型名称',
    },
    {
      key: 'maxValue',
      title: '最大调峰电力（万千瓦）',
      width: '14%',
      sorter: {},
    },
    {
      key: 'maxTime',
      title: '最大调峰时刻',
      width: '9%',
    },
    {
      key: 'period',
      title: '调峰时段',
    },
    {
      key: 'judgment',
      title: '调峰判据',
    },
  ]

  // 引用windPVDetailExportColumns，移除（万千瓦）
  const windPVDetailExportColumns = windPVDetailColumns.map((item) => {
    if (item.title.includes('（万千瓦）')) {
      return {
        ...item,
        title: item.title.replace('（万千瓦）', ''),
      }
    }
    return item
  })

  const time = ref<number>(dayjs().valueOf())

  const useStatus = ref<number>(0) // 0 表示市场

  const description = ref<string>('')
  //调峰时段分析数据
  const peakingPeriodData = ref<PeakingPeriodItem[]>([])

  const windGeneratorDetailList = ref<WindPVGeneratorDetailItem[]>([])

  const pvGeneratorDetailList = ref<WindPVGeneratorDetailItem[]>([])

  const statisticChartData = ref<SeriesData[]>([])

  const windChartData = ref<SeriesData[]>([])

  const solarChartData = ref<SeriesData[]>([])

  const generateChartData = (data: newEnergyDataItemV2[]) => {
    const statisticData: SeriesData[] = []
    const windData: SeriesData[] = []
    const solarData: SeriesData[] = []

    // 为V3版本，我们有5组曲线数据
    for (let i = 0; i < chartData.length; i++) {
      const item = chartData[i]
      statisticData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[0]],
        })),
      })

      windData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[1]],
        })),
      })

      solarData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[2]],
        })),
      })
    }

    statisticChartData.value = statisticData
    windChartData.value = windData
    solarChartData.value = solarData
  }

  const commonStore = useCommonStore()

  const fetchDailyReportData = async (_time?: number) => {
    try {
      commonStore.loading = true
      // 根据需求，statisticMode 字段值为： 0 市场； 1 实际； 2 偏置前； 3 偏置后
      // 对于 V3 页面，我们根据 useStatus 来决定请求哪种模式的数据
      // "市场出清" 曲线的数据直接从返回的 data 中取 totalPower, windPower, pvPower 字段
      const params = {
        day: formatTime(_time || time.value, 'YYYY-MM-DD'),
        statisticMode: useStatus.value // 直接使用 useStatus.value 作为 statisticMode
      }
      
      // 只调用一次接口获取数据
      const response = await DailyReportService.getDailyReportDataFromNari(params)
      
      description.value = response.description + '。'
      peakingPeriodData.value = response.peakingPeriodList
      // maxValue倒序拍
      windGeneratorDetailList.value = response.windGeneratorDetailList.sort(
        (a, b) => Number(b.maxValue) - Number(a.maxValue),
      )
      pvGeneratorDetailList.value = response.pvGeneratorDetailList.sort(
        (a, b) => Number(b.maxValue) - Number(a.maxValue),
      )
      
      // 生成图表数据，所有5条曲线都来自同一份返回的数据
      generateChartData(response.newEnergyDataList)
      // 处理响应数据
    } catch (error) {
      console.error('获取日报数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  return {
    customConfig,
    windPVDetailColumns,
    windPVDetailExportColumns,
    peakingPeriodData,
    peakingPeriodColumns,

    description,
    windGeneratorDetailList,
    pvGeneratorDetailList,
    statisticChartData,
    windChartData,
    solarChartData,
    chartData,
    fetchDailyReportData,
    useStatus,
    time,
  }
}
{
  "extends": "@tsconfig/node22/tsconfig.json",
  "include": [
    "vite.config.*",
    "vitest.config.*",
    "cypress.config.*",
    "nightwatch.conf.*",
    "playwright.config.*",
    "eslint.config.*"
  ],
  "compilerOptions": {
    "noEmit": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",

    "target": "ES2019", // ✅ Chrome 75 支持 ES2019
    "lib": ["DOM", "ES2019"], // ✅ 保证 API 支持 ES2019
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "types": ["node"]
  }
}

import { ref } from 'vue'
import { useCommonStore } from '@/stores'
import type { SeriesData } from '@/types/chart'
import { PeakShavingService, type newEnergyDataItemV2 } from '@/utils/api'
import { formatTime } from '@/utils/tools'

export const usePeakShavingChart = () => {
  const chartData: {
    name: string
    data: CharacterData[]
    color: string
    keys: (keyof newEnergyDataItemV2)[]
  }[] = [
    {
      name: '实际出力',
      data: [],
      color: '#5E42CE',
      keys: ['totalPower', 'windPower', 'pvPower'],
    },
    {
      name: '超短期新能源预测',
      data: [],
      color: '#EDAC00',
      keys: ['totalPrediction', 'windPrediction', 'pvPrediction'],
    },
    {
      name: '偏置前出清',
      data: [],
      color: '#42CE83',
      keys: ['totalBeforePower', 'windBeforePower', 'pvBeforePower'],
    },
    {
      name: '偏置后出清',
      data: [],
      color: '#FF4C1E',
      keys: ['totalAfterPower', 'windAfterPower', 'pvAfterPower'],
    },
  ]

  const customConfig = {
    title: {
      left: -5,
      top: 7,
    },
    grid: {
      top: '25%',
    },
  }

  const commonStore = useCommonStore()

  const useStatus = ref<number>(3) // 3 表示偏置后

  const statisticChartData = ref<SeriesData[]>([])
  const windChartData = ref<SeriesData[]>([])
  const solarChartData = ref<SeriesData[]>([])

  const generateChartData = (data: newEnergyDataItemV2[]) => {
    const statisticData: SeriesData[] = []
    const windData: SeriesData[] = []
    const solarData: SeriesData[] = []

    chartData.forEach((item) => {
      statisticData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[0]],
        })),
      })

      windData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[1]],
        })),
      })

      solarData.push({
        name: item.name,
        color: item.color,
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: dataItem[item.keys[2]],
        })),
      })
    })

    statisticChartData.value = statisticData
    windChartData.value = windData
    solarChartData.value = solarData
  }

  const getPeakShavingChartData = async (time: number) => {
    commonStore.loading = true
    try {
      const res = await PeakShavingService.getPeakShavingChartData({
        day: formatTime(time, 'YYYY-MM-DD'),
        statisticMode: useStatus.value, // 直接使用 useStatus.value 作为 statisticMode
      })
      generateChartData(res.newEnergyDataList)
      return res.newEnergyDataList
    } catch (error) {
      console.error('获取图表数据失败:', error)
      throw error
    } finally {
      commonStore.loading = false
    }
  }

  return {
    customConfig,
    statisticChartData,
    windChartData,
    solarChartData,
    getPeakShavingChartData,
  }
}

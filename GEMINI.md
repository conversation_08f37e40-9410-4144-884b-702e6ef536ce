# 项目：现货市场运行品质分析系统 (Spot Market Operation Quality Analysis System)

## 1. 项目概述

本项目是一个用于电力现货市场的运行品质分析与可视化平台。它旨在为电力调度和交易人员提供一个直观的Web界面，用于监控、分析和评估电力市场的实时运行状态和电能质量。该系统完全基于前端技术实现，通过API获取后端数据进行展示和分析。

## 2. 核心功能

根据项目的文件结构，系统包含以下几个核心功能模块：

*   **CPS频率分析 (`src/views/cps-frequency`)**:
    *   监控和分析电网的控制性能标准（CPS）和系统频率。
    *   提供如图表和统计数据等多种维度的可视化展示。

*   **负荷预测 (`src/views/load-forecasting`)**:
    *   提供对不同颗粒度的电力负荷进行预测分析，包括母线负荷 (`busbar-load`) 和统一负荷 (`unified-load`)。
    *   展示预测曲线和实际负荷曲线的对比。

*   **断面影响分析 (`src/views/section-influence`)**:
    *   **断面监控 (`section-monitoring`)**: 实时监控关键输电断面的潮流和功率。
    *   **削峰填谷分析 (`peak-shaving`)**: 分析电网的峰谷特性，评估调峰措施的效果。

*   **日报 (`src/views/daily-report`)**:
    *   自动整合关键运行指标，生成和展示多种格式的日报。

## 3. 技术栈

*   **框架**: Vue 3
*   **语言**: TypeScript
*   **构建工具**: Vite
*   **状态管理**: Pinia (推断自 `src/stores`)
*   **路由**: Vue Router (推断自 `src/router`)
*   **UI组件库/样式**: UnoCSS (根据 `uno.config.ts`)
*   **数据可视化**: ECharts (推断自 `src/utils/libs/echarts.ts`)

## 4. 项目结构导览

*   `src/`: 核心源代码目录。
    *   `main.ts`: 应用入口文件。
    *   `App.vue`: 根 Vue 组件。
    *   `assets/`: 存放静态资源，如图片和SVG图标。
    *   `components/`: 存放可复用的Vue组件。
        *   `features/`: 特定功能的业务组件。
        *   `shared/`: 通用的共享组件，如按钮、图表、表格等。
    *   `views/`: 存放页面的顶级视图组件，每个子目录对应一个主要功能模块。
    *   `router/`: 存放路由配置。
    *   `stores/`: 存放 Pinia 状态管理模块。
    *   `utils/`: 存放工具函数。
        *   `api/`: 封装了与后端服务进行HTTP通信的逻辑。
        *   `export/`: 包含了导出数据到Excel或Word的功能。
    *   `types/`: 存放 TypeScript 类型定义。

import { graphic } from 'echarts/core'
import type { GradientColors, CustomChartConfig } from '@/types/chart'
import { hexToRgba } from '@/utils/tools'

// 重新导出tooltip相关功能
export {
  createEnhancedTooltipFormatter,
  type CalculatedField,
  type TooltipOptions,
} from './chart/tooltip'

/**
 * 智能深度合并对象
 * 支持数组的智能合并策略：
 * - 对于 yAxis 等配置数组，使用索引合并而不是简单拼接
 * - 对于普通数组，保持原有的拼接行为
 */
export function smartDeepMerge(
  target: any,
  source: any,
  options: { arrayMergeStrategy?: 'concat' | 'index' } = {},
): any {
  if (!source) return target
  if (!target) return source

  // 处理数组的智能合并
  if (Array.isArray(target) && Array.isArray(source)) {
    const strategy = options.arrayMergeStrategy || 'concat'

    if (strategy === 'index') {
      // 索引合并：按索引位置合并数组元素
      const result = [...target]
      source.forEach((item, index) => {
        if (index < result.length) {
          result[index] = smartDeepMerge(result[index], item, options)
        } else {
          result.push(item)
        }
      })
      return result
    } else {
      // 拼接合并：直接拼接数组
      return target.concat(source)
    }
  }

  // 处理对象合并
  if (target && source && typeof target === 'object' && typeof source === 'object') {
    const result = { ...target }

    for (const key in source) {
      if (source[key] !== null && source[key] !== undefined) {
        // 对于 yAxis 等特殊配置，使用索引合并策略
        if (key === 'yAxis' && Array.isArray(source[key])) {
          result[key] = smartDeepMerge(result[key] || [], source[key], {
            arrayMergeStrategy: 'index',
          })
        } else if (typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = smartDeepMerge(result[key] || {}, source[key], options)
        } else {
          result[key] = source[key]
        }
      }
    }

    return result
  }

  return source
}

/**
 * 创建渐变色
 */
export function createGradientColor(
  colors: GradientColors,
  direction: 'vertical' | 'horizontal' = 'vertical',
) {
  const [x1, y1, x2, y2] = direction === 'vertical' ? [0, 0, 0, 1] : [0, 0, 1, 0]

  return new graphic.LinearGradient(x1, y1, x2, y2, [
    { offset: 0, color: colors.start },
    { offset: 1, color: colors.end },
  ])
}

/**
 * 生成渐变色配置
 */
export function generateGradientColors(
  color?: string,
  opacity: { start: number; end: number } = { start: 0.8, end: 0.3 },
): GradientColors {
  if (!color) {
    return {
      start: `rgba(58, 77, 233, ${opacity.start})`,
      end: `rgba(58, 77, 233, ${opacity.end})`,
    }
  }

  // 如果已经是 rgba 格式，直接使用
  if (color.startsWith('rgba')) {
    const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/)
    if (rgbaMatch) {
      const [, r, g, b] = rgbaMatch
      return {
        start: `rgba(${r}, ${g}, ${b}, ${opacity.start})`,
        end: `rgba(${r}, ${g}, ${b}, ${opacity.end})`,
      }
    }
  }

  // 处理 hex 颜色
  return {
    start: hexToRgba(color, opacity.start),
    end: hexToRgba(color, opacity.end),
  }
}

/**
 * 获取纯色（去除透明度）
 */
export function getPureColor(color: string): string {
  if (color.startsWith('rgba')) {
    return color.replace(/,\s*[\d.]+\)/, ', 1)')
  }
  return color
}

/**
 * 合并图表配置
 */
export function mergeChartConfig(defaultConfig: any, customConfig?: CustomChartConfig): any {
  if (!customConfig) return defaultConfig

  return smartDeepMerge(defaultConfig, customConfig)
}

/**
 * 创建响应式图表配置
 */
export function createResponsiveConfig(
  baseConfig: any,
  screenSize: 'small' | 'medium' | 'large' = 'medium',
): any {
  const responsiveSettings = {
    small: {
      title: { textStyle: { fontSize: 14 } },
      legend: { textStyle: { fontSize: 12 }, itemWidth: 12, itemHeight: 12 },
      grid: { left: '5%', right: '5%' },
    },
    medium: {
      title: { textStyle: { fontSize: 16 } },
      legend: { textStyle: { fontSize: 14 }, itemWidth: 14, itemHeight: 14 },
      grid: { left: '3%', right: '4%' },
    },
    large: {
      title: { textStyle: { fontSize: 20 } },
      legend: { textStyle: { fontSize: 16 }, itemWidth: 16, itemHeight: 16 },
      grid: { left: '3%', right: '4%' },
    },
  }

  return smartDeepMerge(baseConfig, responsiveSettings[screenSize])
}

/**
 * 格式化图表数据
 */
export function formatChartData(data: any[], valueKey: string = 'value', nameKey: string = 'name') {
  return data.map((item) => ({
    name: item[nameKey],
    value: item[valueKey],
  }))
}

/**
 * 生成时间轴数据
 */
export function generateTimeAxis(
  startTime: string,
  endTime: string,
  interval: number = 1,
): string[] {
  const start = new Date(startTime)
  const end = new Date(endTime)
  const result: string[] = []

  const current = new Date(start)
  while (current <= end) {
    result.push(current.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }))
    current.setHours(current.getHours() + interval)
  }

  return result
}

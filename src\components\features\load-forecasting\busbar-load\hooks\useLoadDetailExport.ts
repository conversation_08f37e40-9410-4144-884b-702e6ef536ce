import type { BusbarLoadDataDetailResponse } from '@/utils/api/'
import LineChart from '@/components/shared/charts/LineChart.vue'

export const useLoadDetailExport = () => {
  const getExportData = (
    detailData: BusbarLoadDataDetailResponse,
    chartRef: InstanceType<typeof LineChart> | null,
    date = '',
  ) => {
    const { busLfStatisticList } = detailData
    const data = busLfStatisticList.map((item) => {
      return [
        {
          title: '日期',
          value: item.date || date,
          style: {
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFACB9CA' },
            },
            font: {
              bold: true,
            },
          },
        },
        {
          title: '',
          value: [
            {
              title: '名称',
              value: item.name,
            },
            {
              title: '日均偏差',
              value: item.dailyAvgUsDiff,
            },
            {
              title: '日均偏差率',
              value: item.dailyAvgUsDiffRate,
            },
            {
              title: 'RMSE',
              value: item.rmse,
            },
            {
              title: 'MAE',
              value: item.mae,
            },
            {
              title: 'MAPE',
              value: item.mape,
            },
          ],
        },
        {
          title: '时间',
          value: item.busLfDataList.map((item) => item.time),
        },
        {
          title: '短期预测',
          value: item.busLfDataList.map((item) => item.sPrediction),
        },
        {
          title: '超短期预测',
          value: item.busLfDataList.map((item) => item.usPrediction),
        },
        {
          title: '实时负荷',
          value: item.busLfDataList.map((item) => item.rtValue),
        },
        {
          title: '负荷偏差',
          value: item.busLfDataList.map((item) => item.usDiff),
        },
        {
          title: '偏差率',
          value: item.busLfDataList.map((item) => item.usDiffRate),
        },
      ]
    })

    return {
      list: data,
      image: chartRef?.getImg().base64 || '',
    }
  }

  return {
    getExportData,
  }
}

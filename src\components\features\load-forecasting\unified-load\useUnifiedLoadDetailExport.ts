import LineChart from '@/components/shared/charts/LineChart.vue'
import type { UnifiedLoadStatisticItem } from '@/utils/api/services/load-forecasting/unifiedLoad'

type ExportItem = {
  title: string
  value: string | string[] | { title: string; value: string }[]
  style?: any
}

export const useUnifiedLoadDetailExport = () => {
  const getExportData = (
    list: UnifiedLoadStatisticItem[],
    chartRef: InstanceType<typeof LineChart> | null,
    date = '',
  ) => {
    const data = list.map((item) => {
      const res: ExportItem[] = [
        {
          title: '日期',
          value: item.date || date,
          style: {
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFACB9CA' },
            },
            font: {
              bold: true,
            },
          },
        },
        {
          title: '',
          value: [
            {
              title: '日均偏差',
              value: item.dailyAvgUsDiff,
            },
            {
              title: '日均偏差率',
              value: item.dailyAvgUsDiffRate,
            },
            {
              title: 'RMSE',
              value: item.rmse,
            },
            {
              title: 'MAE',
              value: item.mae,
            },
            {
              title: 'MAPE',
              value: item.mape,
            },
          ],
        },
      ]

      if (item.unifiedLfDataList.length) {
        const timeList: string[] = []
        const sPredictionList: string[] = []
        const usPredictionList: string[] = []
        const rtValueList: string[] = []
        const usDiffList: string[] = []
        const usDiffRateList: string[] = []
        item.unifiedLfDataList.forEach((item) => {
          timeList.push(item.time)
          sPredictionList.push(item.sPrediction)
          usPredictionList.push(item.usPrediction)
          rtValueList.push(item.rtValue)
          usDiffList.push(item.usDiff)
          usDiffRateList.push(item.usDiffRate)
        })

        res.push(
          {
            title: '时间',
            value: timeList,
          },
          {
            title: '短期预测',
            value: sPredictionList,
          },
          {
            title: '超短期预测',
            value: usPredictionList,
          },
          {
            title: '实时负荷',
            value: rtValueList,
          },
          {
            title: '负荷偏差',
            value: usDiffList,
          },
          {
            title: '偏差率',
            value: usDiffRateList,
          },
        )
      }
      console.log('🚀 ~ getExportData ~ res:', res)
      return res
    })

    return {
      list: data,
      image: chartRef?.getImg().base64 || '',
    }
  }

  return {
    getExportData,
  }
}

import type { ApiInstanceConfig } from '@/types/api'

/**
 * API配置
 */
export const apiConfig: ApiInstanceConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/dwyzt',
  headers: {
    'Content-Type': 'application/json',
  },
}

/**
 * 开发环境配置
 */
export const devConfig: ApiInstanceConfig = {
  ...apiConfig,
  baseURL: import.meta.env.VITE_API_BASE_URL || '/dwyzt',
}

/**
 * 生产环境配置
 */
export const prodConfig: ApiInstanceConfig = {
  ...apiConfig,
  baseURL: import.meta.env.VITE_API_BASE_URL || '/dwyzt',
}

/**
 * 根据环境获取配置
 */
export function getApiConfig(): ApiInstanceConfig {
  const env = import.meta.env.MODE

  switch (env) {
    case 'development':
      return devConfig
    case 'production':
      return prodConfig
    default:
      return apiConfig
  }
}

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 断面统计相关
  SECTION_STATISTIC: {
    // 断面统计列表
    SECTION_STATISTIC_LIST: '/getSectionStatisticList',
    // 断面统计详情
    SECTION_STATISTIC_DETAIL: '/getSectionStatisticDetail',
    // 获取所有断面数据
    ALL_SECTION_DATA: '/getAllSectionDataList',
    // 获取断面曲线数据（包含计划和市场值）
    SECTION_DATA_WITH_MARKET_AND_PLAN: '/getSectionDataListWithMarketAndPlanByDay',
  },
  // CPS及频率相关
  CPS_FREQUENCY: {
    // CPS及频率数据
    CPS_DATA: '/getCPSStatisticByTime',
    CPS_DATA_BY_RT_KEY_ID: '/getDataByRtKeyId',
  },
  DAILY_REPORT: {
    // 日报数据
    DAILY_REPORT_DATA: '/getNewEnergyDailyStatistic',
    //日报数据v2 getNewEnergyDailyStatisticFromNari
    DAILY_REPORT_DATA_FROM_NARI: '/getNewEnergyDailyStatisticFromNari',
  },
  LOAD_FORECASTING: {
    // 根据时间获取全省母线负荷统计 列表/曲线
    BUSBAR_LOAD_DATA: '/getBusLfProvinceStatistic',
    // 获取全省母线负荷单个时间详情
    BUSBAR_LOAD_DATA_BY_TIME: '/getBusLfProvinceStatisticByTime',
    // 获取全省母线负荷统计信息明细 getBusLfProvinceStatisticDetail
    BUSBAR_LOAD_DATA_DETAIL: '/getBusLfProvinceStatisticDetail',
    // 查询母线负荷下的相关可查询设备列表 getBusLfExistDeviceList
    BUSBAR_LOAD_EXIST_DEVICE_LIST: '/getBusLfExistDeviceList',
    // 获取地市母线负荷单日统计信息 getBusLfStatisticByRegionOneDay
    BUSBAR_LOAD_DATA_BY_REGION: '/getBusLfStatisticByRegionOneDay',
    // 获取地市母线负荷多日统计信息 getBusLfStatisticByRegion
    BUSBAR_LOAD_DATA_BY_REGION_MULTI_DAY: '/getBusLfStatisticByRegion',
    // 获取场站母线负荷统计信息 getBusLfStatisticByStation
    BUSBAR_LOAD_DATA_BY_STATION: '/getBusLfStatisticByStation',
    // 获取设备母线负荷统计信息 getBusLfStatisticByDevice
    BUSBAR_LOAD_DATA_BY_DEVICE: '/getBusLfStatisticByDevice',

    // 获取统调负荷曲线信息 getUnifiedLoadStatistic
    UNIFIED_LOAD_DATA: '/getUnifiedLoadStatistic',
    // 获取统调负荷曲线多日详情 getUnifiedLoadStatisticDetail
    UNIFIED_LOAD_DATA_DETAIL: '/getUnifiedLoadStatisticDetail',
    // 获取统调负荷曲线月度详情 getUnifiedLoadStatisticMonthlyDetail
    UNIFIED_LOAD_DATA_MONTHLY_DETAIL: '/getUnifiedLoadStatisticMonthlyDetail',
  },
} as const

/**
 * 请求状态码
 */
export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  REQUEST_TIMEOUT: 408,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const

/**
 * 业务状态码
 */
export const BUSINESS_CODE = {
  SUCCESS: 200,
  SUCCESS_STR: '0000', // 字符串格式的成功状态码
  FAIL: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
} as const

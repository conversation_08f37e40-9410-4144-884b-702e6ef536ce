<template>
  <SearchForm @search="handleSearch">
    <template #form-fields>
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-300px">
        <n-date-picker
          v-model:value="time"
          class="w-full"
          size="large"
          placeholder="请选择时间"
          :is-date-disabled="disablePreviousDate"
        >
        </n-date-picker>
      </InputGroup>
      <InputGroup size="large" label="断面" class="mr-2.5 max-w-300px">
        <n-select
          v-model:value="selectedSectionId"
          class="w-full"
          :options="sectionOptions"
          size="large"
          placeholder="请选择断面"
          :loading="loadingSections"
          @update:value="handleSectionChange"
        ></n-select>
      </InputGroup>
    </template>
    <template #action-buttons>
      <ExportButton
        class="mr-2.5"
        :filename="filename"
        export-type="excel-structured"
        @export-start="handleExport"
        :structured-data="dataByExport"
      />
    </template>
  </SearchForm>
  <div class="flex px-7.5">
    <div class="w-[calc(100%-480px)] flex-shrink flex-grow">
      <LineChart
        ref="statisticChartRef"
        title="新能源弃电情况统计"
        :data="statisticChartData"
        height="300px"
        :custom-config="customConfig"
      />
      <LineChart
        ref="windChartRef"
        title="风力发电"
        :data="windChartData"
        height="300px"
        :custom-config="customConfig"
      />

      <LineChart
        ref="solarChartRef"
        title="光伏发电"
        :data="solarChartData"
        height="300px"
        :custom-config="customConfig"
      />

      <LineChart
        ref="sectionChartRef"
        title="断面曲线"
        :data="sectionChartData"
        height="300px"
        :custom-config="sectionChartCustomConfig"
        :loading="loadingChart"
      />
    </div>
    <div class="mt-1 w-120">
      <div class="text-#5F6673 text-xl font-bold mb-2.5">当日越限断面</div>
      <DataTable
        height="1050px"
        :columns="peakShavingColumns"
        :data="peakShavingData"
        @sort="handleSort"
        @row-click="handleRowClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, useTemplateRef } from 'vue'

import { NDatePicker, NSelect } from 'naive-ui'
import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'

import { PeakShavingService } from '@/utils/api'
import type { SectionStatisticData } from '@/utils/api/'
import type { ExportBlockOptions } from '@/utils/export'

import { disablePreviousDate, formatTime } from '@/utils/tools'
import { useCommonStore, usePeakShavingStore } from '@/stores'
import { applyDefaultSort, applySorting } from '@/utils/sort'
import { usePeakShavingChart, usePeakShavingExport, useSectionChart } from './hooks'

const peakShavingColumns = [
  {
    title: '断面名称',
    key: 'name',
  },

  {
    title: '电压等级',
    key: 'volt',
    sorter: {},
  },
  {
    title: '最大潮流',
    key: 'maxValue',
    sorter: {},
  },
  {
    title: '最大潮流越限出现时间',
    key: 'maxValueTime',
    sorter: {},
  },
]

const peakShavingStore = usePeakShavingStore()
const commonStore = useCommonStore()

const time = ref(Date.now())

const peakShavingData = ref<SectionStatisticData[]>([])

// 使用 hooks 中的图表数据
const { customConfig, statisticChartData, windChartData, solarChartData, getPeakShavingChartData } =
  usePeakShavingChart()

// 使用断面图表的 hooks
const {
  sectionOptions,
  selectedSectionId,
  loadingSections,
  sectionChartRef,
  chartData: sectionChartData,
  loadingChart,
  customConfig: sectionChartCustomConfig,
  fetchAllSections,
  fetchSectionCurveData,
  selectSectionById,
} = useSectionChart()

const handleSearch = () => {
  peakShavingStore.time = time.value
  getPeakShavingChartData(time.value)
  getPeakShavingDataList()
  fetchSectionCurveData() // 重新加载断面曲线数据
}

const getPeakShavingDataList = async () => {
  commonStore.loading = true
  try {
    const res = await PeakShavingService.getPeakShavingDataList({
      startTime: formatTime(time.value, 'YYYY-MM-DD'),
      endTime: formatTime(time.value, 'YYYY-MM-DD'),
    })
    peakShavingData.value = applyDefaultSort(res)
  } catch (error) {
    console.error('获取当日越限断面失败:', error)
  } finally {
    commonStore.loading = false
  }
}

const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    peakShavingData.value = applyDefaultSort(peakShavingData.value)
    return
  }

  // 使用公共的多列排序函数
  peakShavingData.value = applySorting(peakShavingData.value, multiSortState, peakShavingColumns)
}

// 处理表格行点击事件
const handleRowClick = (row: any) => {
  // 假设row对象中有sectionId属性，如果不是，需要根据实际数据结构调整
  if (row.id) {
    selectSectionById(row.id)
  }
}

// 处理断面选择变化
const handleSectionChange = () => {
  fetchSectionCurveData()
}

// 导出相关
const statisticChartRef = useTemplateRef('statisticChartRef')
const windChartRef = useTemplateRef('windChartRef')
const solarChartRef = useTemplateRef('solarChartRef')
const dataByExport = ref<ExportBlockOptions>()

const filename = computed(() => {
  return `新能源弃电情况统计_${formatTime(time.value, 'YYYY-MM-DD')}`
})

const { getExportData } = usePeakShavingExport()

const handleExport = () => {
  dataByExport.value = getExportData({
    statisticChartRef: statisticChartRef.value,
    windChartRef: windChartRef.value,
    solarChartRef: solarChartRef.value,
    sectionChartRef: sectionChartRef.value,
    tableData: peakShavingData.value,
    date: formatTime(time.value, 'YYYY-MM-DD'),
  })
}

onMounted(() => {
  getPeakShavingChartData(time.value)
  getPeakShavingDataList()
  fetchAllSections()
})
</script>

import type { MenuOption, SelectOption } from 'naive-ui'
import { renderIcon, renderText } from '../tools'
import icons from './icons'

export const getCityRawOptions = (type: string): MenuOption[] => {
  const route = '/load-forecasting/busbar-load' + (type === 'plan' ? '-plan' : '-spot')
  return [
    {
      label: renderText('全省', 'thirdLevel'),
      key: `${type}-province`,
      region: 'province',
      type: 'province',
      name: '全省',
      route,
    },
    {
      label: renderText('南京', 'thirdLevel'),
      key: `${type}-all-nanjing`,
      regionCode: '320100',
      children: [
        {
          label: '南京',
          key: `${type}-nanjing`,
          region: '南京',
          regionCode: '320100',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('无锡', 'thirdLevel'),
      key: `${type}-all-wuxi`,
      regionCode: '320200',
      children: [
        {
          label: '无锡',
          key: `${type}-wuxi`,
          region: '无锡',
          regionCode: '320200',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('徐州', 'thirdLevel'),
      key: `${type}-all-xuzhou`,
      regionCode: '320300',
      children: [
        {
          label: '徐州',
          key: `${type}-xuzhou`,
          region: '徐州',
          regionCode: '320300',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('常州', 'thirdLevel'),
      key: `${type}-all-changzhou`,
      regionCode: '320400',
      children: [
        {
          label: '常州',
          key: `${type}-changzhou`,
          region: '常州',
          regionCode: '320400',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('苏州', 'thirdLevel'),
      key: `${type}-all-suzhou`,
      regionCode: '320500',
      children: [
        {
          label: '苏州',
          key: `${type}-suzhou`,
          region: '苏州',
          regionCode: '320500',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('南通', 'thirdLevel'),
      key: `${type}-all-nantong`,
      regionCode: '320600',
      children: [
        {
          label: '南通',
          key: `${type}-nantong`,
          region: '南通',
          regionCode: '320600',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('连云港', 'thirdLevel'),
      key: `${type}-all-lianyungang`,
      regionCode: '320700',
      children: [
        {
          label: '连云港',
          key: `${type}-lianyungang`,
          region: '连云港',
          regionCode: '320700',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('淮安', 'thirdLevel'),
      key: `${type}-all-huaian`,
      regionCode: '320800',
      children: [
        {
          label: '淮安',
          key: `${type}-huaian`,
          region: '淮安',
          regionCode: '320800',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('盐城', 'thirdLevel'),
      key: `${type}-all-yancheng`,
      regionCode: '320900',
      children: [
        {
          label: '盐城',
          key: `${type}-yancheng`,
          region: '盐城',
          regionCode: '320900',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('扬州', 'thirdLevel'),
      key: `${type}-all-yangzhou`,
      regionCode: '321000',
      children: [
        {
          label: '扬州',
          key: `${type}-yangzhou`,
          region: '扬州',
          regionCode: '321000',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('镇江', 'thirdLevel'),
      key: `${type}-all-zhenjiang`,
      regionCode: '321100',
      children: [
        {
          label: '镇江',
          key: `${type}-zhenjiang`,
          region: '镇江',
          regionCode: '321100',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('泰州', 'thirdLevel'),
      key: `${type}-all-taizhou`,
      regionCode: '321200',
      children: [
        {
          label: '泰州',
          key: `${type}-taizhou`,
          region: '泰州',
          regionCode: '321200',
          type: 'city',
          route,
        },
      ],
    },
    {
      label: renderText('宿迁', 'thirdLevel'),
      key: `${type}-all-suqian`,
      regionCode: '321300',
      children: [
        {
          label: '宿迁',
          key: `${type}-suqian`,
          region: '宿迁',
          regionCode: '321300',
          type: 'city',
          route,
        },
      ],
    },
  ]
}

export const menuRawOptions: MenuOption[] = [
  {
    label: renderText('断面影响', 'firstLevel'),
    key: 'section-influence',
    icon: renderIcon(icons.sectionMonitoringIcon),
    children: [
      {
        label: renderText('断面影响调峰情况', 'secondLevel'),
        key: 'section-influence-peak-shaving',
      },
      {
        label: renderText('断面监视', 'secondLevel'),
        key: 'section-monitoring',
      },
    ],
  },
  {
    label: renderText('执行情况', 'firstLevel'),
    key: 'execution-situation',
    icon: renderIcon(icons.executionSituationIcon),
    children: [
      // {
      //   label: renderText('参与市场', 'secondLevel'),
      //   key: 'participate-market',
      // },
      // {
      //   label: renderText('煤炭', 'secondLevel'),
      //   key: 'coal',
      //   children: [
      //     {
      //       label: renderText('全省', 'thirdLevel'),
      //       key: 'all-province',
      //     },
      //     {
      //       label: renderText('江南', 'thirdLevel'),
      //       key: 'jiangnan',
      //     },
      //     {
      //       label: renderText('江北', 'thirdLevel'),
      //       key: 'jiangbei',
      //     },
      //   ],
      // },
      // {
      //   label: renderText('核电', 'secondLevel'),
      //   key: 'nuclear-power',
      // },
      // {
      //   label: renderText('风电', 'secondLevel'),
      //   key: 'wind-power',
      // },
      // {
      //   label: renderText('光伏', 'secondLevel'),
      //   key: 'photovoltaic',
      // },
      // {
      //   label: renderText('燃煤', 'secondLevel'),
      //   key: 'coal-fired',
      // },
      // {
      //   label: renderText('燃气', 'secondLevel'),
      //   key: 'gas',
      // },
      // {
      //   label: renderText('抽蓄', 'secondLevel'),
      //   key: 'pumped-storage',
      // },
      // {
      //   label: renderText('独立储能', 'secondLevel'),
      //   key: 'independent-storage',
      // },
      // {
      //   label: renderText('不参与市场', 'secondLevel'),
      //   key: 'not-participate-market',
      // },
      // {
      //   label: renderText('其他', 'secondLevel'),
      //   key: 'other',
      // },
      {
        label: renderText('新能源市场调峰日报', 'secondLevel'),
        key: 'daily-report',
      },
      {
        label: renderText('新能源市场调峰日报V2', 'secondLevel'),
        key: 'daily-report-v2',
      },
      // {
      //   label: renderText('新能源市场调峰日报V3', 'secondLevel'),
      //   key: 'daily-report-v3',
      // },
    ],
  },
  {
    label: renderText('负荷预测', 'firstLevel'),
    key: 'load-forecasting',
    icon: renderIcon(icons.loadForecastingIcon),
    children: [
      {
        label: renderText('统调负荷', 'secondLevel'),
        key: 'unified-load',
        children: [
          {
            label: renderText('全省', 'thirdLevel'),
            key: 'unified-load-province',
            type: 'province',
            name: '全省',
            route: '/load-forecasting/unified-load',
          },
        ],
      },
      {
        label: renderText('母线负荷（现货）', 'secondLevel'),
        key: 'busbar-load-spot',
        children: [],
      },
      {
        label: renderText('母线负荷（计划）', 'secondLevel'),
        key: 'busbar-load-plan',
        children: [],
      },
    ],
  },
  {
    label: renderText('CPS及频率', 'firstLevel'),
    key: 'cps-frequency',
    icon: renderIcon(icons.cpsFrequencyIcon),
  },
]

export const SortOptions: SelectOption[] = [
  {
    label: '按日均偏差',
    value: 'dailyAvgUsDiff',
  },
  {
    label: '按日均偏差率',
    value: 'dailyAvgUsDiffRate',
  },
  {
    label: '按MAPE',
    value: 'mape',
  },
  {
    label: '按RMSE',
    value: 'rmse',
  },
  {
    label: '按MAE',
    value: 'mae',
  },
]

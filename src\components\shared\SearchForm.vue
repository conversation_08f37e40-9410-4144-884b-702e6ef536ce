<template>
  <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <div class="flex flex-1">
      <slot name="form-fields"></slot>
    </div>
    <div class="flex">
      <n-button
        type="info"
        class="mr-2.5"
        size="large"
        @click="handleSearch"
        :loading="commonStore.loading"
      >
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        查询
      </n-button>
      <n-button class="mr-2.5" size="large" @click="handleReset" v-if="showReset">
        <template #icon>
          <n-icon><ArrowClockwise20FilledIcon /></n-icon>
        </template>
        重置
      </n-button>
      <slot name="action-buttons"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui'
import { SearchIcon, ArrowClockwise20FilledIcon } from '@/utils/constant/icons'
import { useCommonStore } from '@/stores'

const commonStore = useCommonStore()

// 定义 props
interface Props {
  showReset?: boolean
}

withDefaults(defineProps<Props>(), {
  showReset: false,
})

// 定义 emits
const emit = defineEmits<{
  search: []
  reset: []
}>()

// 搜索处理函数
const handleSearch = () => {
  emit('search')
}

// 重置处理函数
const handleReset = () => {
  emit('reset')
}
</script>

<style scoped></style>

<template>
  <KeepAlive :include="['ProvinceStatistic']">
    <component :is="components"></component>
  </KeepAlive>
</template>

<script setup lang="ts">
import ProvinceStatistic from './statistic/ProvinceStatistic.vue'
import ProvinceDetail from './statistic/ProvinceDetail.vue'
import ProvinceInfo from './info/ProvinceInfo.vue'

import { useBusbarLoadStore } from '@/stores'
import { computed } from 'vue'

const busbarLoadStore = useBusbarLoadStore()

/**
 * 默认显示ProvinceStatistic
 * 选择时间查询后，显示ProvinceDetail
 * 点击ProvinceStatistic中的详情按钮，显示ProvinceInfo
 */
const components = computed(() => {
  console.log(
    '🚀 ~ components ~ busbarLoadStore.isProvinceInfoVisible:',
    busbarLoadStore.isProvinceInfoVisible,
  )
  console.log(
    '🚀 ~ components ~ busbarLoadStore.isProvinceDetailVisible:',
    busbarLoadStore.isProvinceDetailVisible,
  )
  if (busbarLoadStore.isProvinceInfoVisible) {
    return ProvinceInfo
  }
  if (busbarLoadStore.isProvinceDetailVisible) {
    return ProvinceDetail
  }
  return ProvinceStatistic
})
</script>

<style scoped></style>

/**
 * Word 文档内容类型定义
 * 支持文本、表格、图片等多种内容类型
 * 使用 docx 库提供的原生类型确保兼容性
 */
import type { HeadingLevel, AlignmentType, ISpacingProperties, IRunOptions } from 'docx'

// Word 内容项联合类型
export type WordContentItem = TextBlock | TableBlock | ImageBlock

// 基础块接口
export interface BaseBlock {
  type: 'text' | 'table' | 'image'
}

// 使用 docx 原生的间距配置
export type SpacingConfig = ISpacingProperties

// 简化的编号配置（兼容现有代码）
export interface NumberingConfig {
  reference: string
  level: number
}

// 使用 docx 原生的文本样式配置
export type TextStyleConfig = IRunOptions

// 文本块样式配置
export interface TextStyleOptions {
  heading?: (typeof HeadingLevel)[keyof typeof HeadingLevel]
  alignment?: (typeof AlignmentType)[keyof typeof AlignmentType]
  spacing?: SpacingConfig
  indentFirstLine?: boolean
  numbering?: NumberingConfig
}

// 表格样式配置
export interface TableStyleConfig {
  width?: number
  borderColor?: string
  borderWidth?: number
  fontSize?: number
  headerBold?: boolean
  cellPadding?: number
  alignment?: (typeof AlignmentType)[keyof typeof AlignmentType]
}

// 图片配置
export interface ImageConfig {
  width?: number
  height?: number
  alignment?: (typeof AlignmentType)[keyof typeof AlignmentType]
  caption?: string
}

// 文本块接口
export interface TextBlock extends BaseBlock {
  type: 'text'
  data: string
  style?: TextStyleOptions
  textStyle?: TextStyleConfig
}

// 表格块接口
export interface TableBlock extends BaseBlock {
  type: 'table'
  data: string[][]
  style?: TableStyleConfig
}

// 图片块接口
export interface ImageBlock extends BaseBlock {
  type: 'image'
  data?: string // base64 string (data:image/png;base64,...)
  style?: ImageConfig
}

// 导出选项
export interface WordExportOptions {
  filename?: string
  pageSize?: 'A4' | 'A3' | 'Letter' | 'Legal'
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top?: number
    right?: number
    bottom?: number
    left?: number
  }
}

// 文档配置
export interface DocumentConfig {
  title?: string
  author?: string
  subject?: string
  keywords?: string[]
  description?: string
  creator?: string
  revision?: string
  createdAt?: Date
  modifiedAt?: Date
}

// 完整的 Word 文档结构
export interface WordDocument {
  content: WordContentItem[]
  config?: DocumentConfig
  options?: WordExportOptions
}

// 预设样式类型
export type PresetStyleType =
  | 'title'
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'paragraph'
  | 'caption'

// 预设样式配置
export interface PresetStyle {
  textStyle: TextStyleConfig
  style: TextStyleOptions
}

// 样式预设映射
export type StylePresets = Record<PresetStyleType, PresetStyle>

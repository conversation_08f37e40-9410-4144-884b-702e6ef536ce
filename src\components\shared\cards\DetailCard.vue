<template>
  <div class="flex bg-#FEF4E8 rounded mx-7.5">
    <div
      v-for="(item, index) in data"
      :key="index"
      class="flex flex-col px-5 py-4 flex-auto flex-grow-1"
    >
      <span class="text-#CC8F60 text-base flex items-center font-normal"
        ><span class="text-5xl mr-1">·</span> {{ item.label }}</span
      >
      <span class="text-#643F1A text-xl font-medium ml-3.5">{{ item.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  data: Record<string, any>
}>()
</script>

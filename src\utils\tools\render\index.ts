import { NIcon } from 'naive-ui'
import { h, type Component } from 'vue'

const textClassOptions = {
  firstLevel: 'text-xl font-bold',
  secondLevel: 'text-xl font-400',
  thirdLevel: 'text-base font-normal',
} as const

export type TextLevel = keyof typeof textClassOptions

export function renderText(label: string, textLevel: TextLevel) {
  return () =>
    h(
      'span',
      { class: textClassOptions[textLevel] || textClassOptions.firstLevel },
      { default: () => label },
    )
}

export const renderIcon = (icon: Component) => {
  return () =>
    h(
      NIcon,
      {
        size: 28,
      },
      { default: () => h(icon) },
    )
}

import LineChart from '@/components/shared/charts/LineChart.vue'
import type { SectionStatisticData } from '@/utils/api'
import type { ExportBlockOptions } from '@/utils/export'

interface ExportParams {
  statisticChartRef: InstanceType<typeof LineChart> | null
  windChartRef: InstanceType<typeof LineChart> | null
  solarChartRef: InstanceType<typeof LineChart> | null
  sectionChartRef: InstanceType<typeof LineChart> | null
  tableData: SectionStatisticData[]
  date: string
}

type ExportItem = {
  title: string
  value: string | string[] | { title: string; value: string }[]
  style?: any
}

export const usePeakShavingExport = () => {
  /**
   * 获取图表数据摘要信息
   */
  const getChartSummary = (chartRef: InstanceType<typeof LineChart> | null, chartName: string) => {
    if (!chartRef) return `${chartName}: 无数据`

    try {
      // 尝试获取图表的基本信息
      return `${chartName}: 图表数据已生成`
    } catch {
      return `${chartName}: 数据获取失败`
    }
  }

  const getExportData = (params: ExportParams): ExportBlockOptions => {
    const { statisticChartRef, windChartRef, solarChartRef, sectionChartRef, tableData, date } =
      params

    // 构建导出数据结构
    const data: ExportItem[][] = []

    // 第一部分：日期和概览信息
    const overviewSection: ExportItem[] = [
      {
        title: '日期',
        value: date,
        style: {
          fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFACB9CA' },
          },
          font: {
            bold: true,
          },
        },
      },
      {
        title: '报告类型',
        value: '新能源弃电情况统计分析',
        style: {
          font: {
            bold: true,
          },
        },
      },
      {
        title: '数据来源',
        value: [
          {
            title: '新能源弃电情况统计',
            value: getChartSummary(statisticChartRef, '新能源弃电情况统计'),
          },
          { title: '风力发电', value: getChartSummary(windChartRef, '风力发电') },
          { title: '光伏发电', value: getChartSummary(solarChartRef, '光伏发电') },
          { title: '断面曲线', value: getChartSummary(sectionChartRef, '断面曲线') },
          { title: '当日越限断面', value: `共${tableData.length}个断面数据` },
        ],
      },
    ]

    data.push(overviewSection)

    // 第二部分：当日越限断面表格数据
    if (tableData.length > 0) {
      const tableSection: ExportItem[] = [
        {
          title: '当日越限断面统计',
          value: '',
          style: {
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFACB9CA' },
            },
            font: {
              bold: true,
            },
          },
        },
        {
          title: '统计信息',
          value: [
            { title: '总断面数量', value: `${tableData.length}个` },
            { title: '数据统计时间', value: date },
            { title: '数据类型', value: '断面潮流越限分析' },
          ],
        },
        {
          title: '断面名称',
          value: tableData.map((item) => item.name || ''),
        },
        {
          title: '电压等级',
          value: tableData.map((item) => item.volt || ''),
        },
        {
          title: '最大潮流',
          value: tableData.map((item) => item.maxValue || ''),
        },
        {
          title: '最大潮流越限出现时间',
          value: tableData.map((item) => item.maxValueTime || ''),
        },
      ]

      data.push(tableSection)
    } else {
      // 如果没有表格数据，添加说明
      const emptyTableSection: ExportItem[] = [
        {
          title: '当日越限断面统计',
          value: '当日无越限断面数据',
          style: {
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFFFFFFF' },
            },
            font: {
              italic: true,
            },
          },
        },
      ]
      data.push(emptyTableSection)
    }

    // 获取图表图片，优先使用新能源弃电情况统计图表
    let chartImage = ''
    if (statisticChartRef?.getImg) {
      chartImage = statisticChartRef.getImg().base64 || ''
    } else if (windChartRef?.getImg) {
      chartImage = windChartRef.getImg().base64 || ''
    } else if (solarChartRef?.getImg) {
      chartImage = solarChartRef.getImg().base64 || ''
    } else if (sectionChartRef?.getImg) {
      chartImage = sectionChartRef.getImg().base64 || ''
    }

    return {
      list: data,
      image: chartImage,
    }
  }

  return {
    getExportData,
  }
}

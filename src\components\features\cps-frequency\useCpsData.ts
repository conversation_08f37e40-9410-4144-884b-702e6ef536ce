//  cpsData相关 hook

import { useCpsFrequencyStore, useCommonStore } from '@/stores'
import { CPSFrequencyService, type CPSStatisticQueryParams } from '@/utils/api'
import { autoExecute, formatNumber, formatTime, getTodayTimeRange } from '@/utils/tools'
import { onBeforeUnmount, onMounted, ref } from 'vue'

const RT_KEY_LIST = [
  {
    label: 'CPS1',
    value: '130010:320000000000010129',
  },
  {
    label: 'CPS2',
    value: '130010:320000000000010078',
  },
  {
    label: '全天费用',
    value: '130037:320000000000010009',
  },
  {
    label: '频率',
    value: '103054:320000000000900006',
  },
]

export const useCpsData = () => {
  const cpsFrequencyStore = useCpsFrequencyStore()
  const commonStore = useCommonStore()
  const rtKeyData = ref<(string | number)[]>([])

  // 获取CPS数据
  const fetchCPSData = async () => {
    commonStore.loading = true
    const timestampRange = getTodayTimeRange()

    try {
      const params: CPSStatisticQueryParams = {
        startTime: formatTime(timestampRange[0]),
        endTime: formatTime(timestampRange[1]),
      }

      if (cpsFrequencyStore.isInfoVisible) {
        params.startTime = formatTime(cpsFrequencyStore.timeRange[0])
        params.endTime = formatTime(cpsFrequencyStore.timeRange[1])
      }

      const response = await CPSFrequencyService.getCPSStatisticByTime(params)
      cpsFrequencyStore.cpsData = response
    } catch (error) {
      console.error('获取CPS数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  const fetchCPSDataByRtKeyId = async () => {
    // 这个函数不需要显示全局loading，因为它是定时执行的
    try {
      const response = await CPSFrequencyService.getDataByRtKeyId({
        rtKeyStr: RT_KEY_LIST.map((item) => item.value).join(','),
      })
      rtKeyData.value = response.map((item) => formatNumber(item))
    } catch (error) {
      console.error('获取CPS实时值失败:', error)
    }
  }

  let clearCPSDataByRtKeyIdInterval: () => void
  let clearCPSDataInterval: () => void

  // 组件挂载时初始化
  onMounted(() => {
    clearCPSDataByRtKeyIdInterval = autoExecute(fetchCPSDataByRtKeyId)
    if (cpsFrequencyStore.isInfoVisible) return
    clearCPSDataInterval = autoExecute(fetchCPSData)
  })

  onBeforeUnmount(() => {
    clearCPSDataByRtKeyIdInterval()
    clearCPSDataInterval()
  })
  return {
    rtKeyData,
  }
}

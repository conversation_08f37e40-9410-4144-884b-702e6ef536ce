// 按需加载 Excel 导出工具
import { saveAs } from '.'

export interface ExportColumn {
  key: string
  title: string
  width?: number
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  sheetName?: string
  columns?: ExportColumn[]
  data: any[]
}

export interface ExportBlock {
  title: string
  value: string | ExportBlock[] | string[]
  style?: any
}

export interface ExportBlockOptions {
  list?: ExportBlock[][]
  image?: string
}

// 缓存动态 import
// 定义模块类型以保留类型信息
let exceljsModules: Promise<typeof import('exceljs')> | null = null
function loadExcelJS(): Promise<typeof import('exceljs')> {
  if (!exceljsModules) {
    exceljsModules = import('exceljs')
  }
  return exceljsModules
}

/**
 * 导出为 Excel 格式（按需加载 exceljs）
 */
export const exportToExcel = async (options: ExportOptions) => {
  const { data, columns = [], filename = 'export_data', sheetName = 'Sheet1' } = options
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  const { Workbook } = await loadExcelJS()
  const workbook = new Workbook()
  const sheet = workbook.addWorksheet(sheetName)

  // 设置列
  sheet.columns = columns.map((col) => ({
    header: col.title,
    key: col.key,
    width: col.width || col.title.length + 2,
  }))

  // 插入数据
  data.forEach((row) => {
    const newRow: Record<string, any> = {}
    columns.forEach((col) => {
      const rawValue = row[col.key]
      newRow[col.key] = col.formatter ? col.formatter(rawValue) : rawValue
    })
    sheet.addRow(newRow)
  })

  // 样式：加粗表头
  sheet.getRow(1).font = { bold: true }

  // 自动调整列宽
  sheet.columns.forEach((col) => {
    let maxLen = col.header?.toString().length || 10
    sheet.eachRow((row) => {
      const cell = row.getCell(col.key as string)
      const len = cell.value?.toString().length || 0
      if (len > maxLen) maxLen = len
    })
    col.width = maxLen + 2
  })

  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })
  saveAs(blob, `${filename}.xlsx`)
}

/**
 * 导出结构化 Excel（按需加载 exceljs）
 */
export const exportStructuredExcel = async (filename: string, options?: ExportBlockOptions) => {
  if (!options?.list) {
    window.$message?.warning('没有数据可导出')
    return
  }
  const { list, image } = options

  const { Workbook } = await loadExcelJS()
  const workbook = new Workbook()
  const sheet = workbook.addWorksheet('数据')

  // 插入图片
  if (image) {
    const base64 = image.replace(/^data:image\/\w+;base64,/, '')
    const imageId = workbook.addImage({ base64, extension: 'png' })
    sheet.addImage(imageId, { tl: { col: 0, row: 0 }, ext: { width: 600, height: 300 } })
  }

  let currentRow = 18
  for (const rows of list) {
    for (const block of rows) {
      const row = sheet.getRow(currentRow)
      if (block.title) {
        row.getCell(1).value = block.title + '：'
        if (Array.isArray(block.value)) {
          ;(block.value as any[]).forEach((item, idx) => {
            if (Array.isArray(item)) {
              ;(item as any[]).forEach((sub, j) => (row.getCell(j + 2).value = sub))
            } else if (typeof item === 'string') {
              row.getCell(idx + 2).value = item
            }
          })
        } else {
          row.getCell(2).value = block.value as string
        }
        if (block.style) {
          const cell1 = row.getCell(1)
          const cell2 = row.getCell(2)
          Object.assign(cell1, block.style)
          Object.assign(cell2, block.style)
        }
      } else {
        if (Array.isArray(block.value)) {
          ;(block.value as any[]).forEach((item, idx) => {
            if (typeof item === 'string') {
              row.getCell(idx + 1).value = item
            } else {
              row.getCell(idx * 2 + 1).value = item.title + '：'
              row.getCell(idx * 2 + 2).value = (item as any).value
            }
          })
        } else {
          row.getCell(1).value = block.value as string
        }
      }
      currentRow++
    }
    currentRow++
  }

  // 自动设置列宽
  sheet.columns.forEach((col) => {
    let maxLength = 10
    col.eachCell?.((cell) => {
      const len = String(cell.value ?? '').length
      if (len > maxLength) maxLength = len
    })
    col.width = maxLength + 2
  })

  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument-spreadsheetml.sheet',
  })
  saveAs(blob, `${filename}.xlsx`)
}

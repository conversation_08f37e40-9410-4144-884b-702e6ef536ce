# 图表组件使用指南

## 概述

本项目提供了优化后的 `LineChart` 和 `BarChart` 组件，支持智能配置合并、多Y轴、渐变色等功能。

## 主要优化点

### 1. 智能深度合并 (smartDeepMerge)

新的合并策略支持：

- **数组索引合并**：对于 `yAxis` 等配置数组，按索引位置合并而不是简单拼接
- **对象深度合并**：递归合并嵌套对象
- **智能策略选择**：根据配置类型自动选择合并策略

```typescript
// 示例：传入多个Y轴配置
const customConfig = {
  yAxis: [
    { name: '自定义主轴名称' }, // 会合并到第一个Y轴
    { name: '自定义副轴名称' }, // 会合并到第二个Y轴
  ],
}
```

### 2. 渐变色优化

提供了更简洁的渐变色配置方式：

```typescript
// 方式1：在数据中直接指定渐变色
const seriesData = [
  {
    name: '系列1',
    data: [...],
    gradientColors: {
      start: 'rgba(58, 77, 233, 0.8)',
      end: 'rgba(58, 77, 233, 0.3)'
    }
  }
]

// 方式2：只指定颜色，自动生成渐变
const seriesData = [
  {
    name: '系列1',
    data: [...],
    color: '#3A4DE9',  // 会自动生成对应的渐变色
    isShowAreaStyle: true  // 启用面积样式
  }
]
```

### 3. 多Y轴支持

支持最多3个Y轴（主轴、频率轴、差额轴）：

```typescript
const seriesData = [
  { name: '主数据', data: [...], yAxisIndex: 0 },      // 主Y轴
  { name: '频率数据', data: [...], yAxisIndex: 1 },    // 频率Y轴
  { name: '差额数据', data: [...], yAxisIndex: 2 }     // 差额Y轴
]
```

## 组件使用示例

### LineChart 基础用法

```vue
<template>
  <LineChart
    title="电力负荷曲线"
    :data="chartData"
    height="400px"
    yAxisName="兆瓦"
    :custom-config="customConfig"
  />
</template>

<script setup>
import LineChart from '@/components/shared/charts/LineChart.vue'

const chartData = [
  {
    name: '实际负荷',
    data: [
      { name: '00:00', value: 1200 },
      { name: '01:00', value: 1150 },
      // ...
    ],
    color: '#3A4DE9',
    isShowAreaStyle: true,
  },
  {
    name: '预测负荷',
    data: [
      { name: '00:00', value: 1180 },
      { name: '01:00', value: 1160 },
      // ...
    ],
    color: '#FF6B37',
  },
]

const customConfig = {
  title: {
    left: 10,
    textStyle: { fontSize: 18 },
  },
  legend: {
    top: 10,
    itemHeight: 8,
  },
}
</script>
```

### BarChart 基础用法

```vue
<template>
  <BarChart title="地区负荷统计" :data="chartData" height="350px" :custom-config="customConfig" />
</template>

<script setup>
import BarChart from '@/components/shared/charts/BarChart.vue'

const chartData = [
  {
    name: '实际负荷',
    data: [
      { name: '南京', value: 2500 },
      { name: '苏州', value: 2200 },
      { name: '无锡', value: 1800 },
      // ...
    ],
    color: '#3075F6',
  },
  {
    name: '预测负荷',
    data: [
      { name: '南京', value: 2480 },
      { name: '苏州', value: 2180 },
      { name: '无锡', value: 1820 },
      // ...
    ],
    color: '#FFB637',
  },
]

const customConfig = {
  legend: {
    icon: 'rect',
    itemWidth: 14,
    itemHeight: 14,
  },
}
</script>
```

### 高级配置示例

#### 多Y轴配置

```vue
<template>
  <LineChart title="电力系统综合监控" :data="multiAxisData" :custom-config="multiAxisConfig" />
</template>

<script setup>
const multiAxisData = [
  {
    name: '有功功率',
    data: [...],
    yAxisIndex: 0,  // 主Y轴
    color: '#3A4DE9'
  },
  {
    name: '系统频率',
    data: [...],
    yAxisIndex: 1,  // 频率Y轴
    color: '#FF6B37'
  },
  {
    name: '功率差额',
    data: [...],
    yAxisIndex: 2,  // 差额Y轴
    color: '#00C853'
  }
]

const multiAxisConfig = {
  yAxis: [
    { name: '功率(MW)' },      // 自定义主轴名称
    { name: '频率(Hz)' },      // 自定义频率轴名称
    { name: '差额(MW)' }       // 自定义差额轴名称
  ]
}
</script>
```

#### 自定义样式配置

```vue
<script setup>
const customConfig = {
  title: {
    left: 'center',
    textStyle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#2C3E50',
    },
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'middle',
    icon: 'circle',
  },
  grid: {
    left: '5%',
    right: '15%',
    top: '15%',
    bottom: '10%',
  },
}
</script>
```

## 工具函数

项目还提供了一些实用的图表工具函数：

```typescript
import {
  smartDeepMerge,
  generateGradientColors,
  createGradientColor,
  formatChartData,
  generateTimeAxis,
} from '@/utils/chart'

// 生成渐变色
const gradient = generateGradientColors('#3A4DE9', { start: 0.8, end: 0.2 })

// 格式化数据
const formattedData = formatChartData(rawData, 'power', 'time')

// 生成时间轴
const timeAxis = generateTimeAxis('2024-01-01 00:00', '2024-01-01 23:59', 1)
```

import { API_ENDPOINTS } from '../config'
import { api } from '../instance'

// CPS数据项接口
export interface CPSDataItem {
  value: string
  time: string
}

// CPS统计查询参数接口
export interface CPSStatisticQueryParams {
  startTime?: string
  endTime?: string
  name?: string
}

export interface GetDataByRtKeyIdParams {
  rtKeyStr: string
}

export interface CPSStatisticItem {
  allDayFee: string
  cps1: string
  cps2: string
  cps1List: CPSDataItem[]
  cps2List: CPSDataItem[]
  date: string
  maxRate: string
  minRate: string
}

// CPS统计响应数据接口
export interface CPSStatisticResponse {
  CPS2List: CPSDataItem[]
  CPS1List: CPSDataItem[]
  JSRateList: CPSDataItem[]
  CPSStatisticList: CPSStatisticItem[]
}

// CPS及频率服务类
export class CPSFrequencyService {
  /**
   * 获取CPS统计数据
   * @param params 查询参数
   * @returns CPS统计数据
   */
  static async getCPSStatisticByTime(
    params: CPSStatisticQueryParams,
  ): Promise<CPSStatisticResponse> {
    return api.get<CPSStatisticResponse>(API_ENDPOINTS.CPS_FREQUENCY.CPS_DATA, params)
  }

  static async getDataByRtKeyId(params: GetDataByRtKeyIdParams): Promise<string[]> {
    return api.get(API_ENDPOINTS.CPS_FREQUENCY.CPS_DATA_BY_RT_KEY_ID, params)
  }
}

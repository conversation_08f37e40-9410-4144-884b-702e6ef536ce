<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="unifiedLoadStore.timeRange"
            class="w-full"
            type="daterange"
            size="large"
            placeholder="请选择时间范围"
            :is-date-disabled="disablePreviousDate"
          >
          </n-date-picker>
        </InputGroup>
      </template>

      <template #action-buttons>
        <ExportButton :data="tableData" :columns="tableColumns" filename="全省统调负荷数据" />
      </template>
    </SearchForm>
    <LineChart ref="chartRef" :data="chartData" yAxisName="万千瓦" height="450px" class="mt-2" />

    <DetailCard :data="unifiedLoadCardData"></DetailCard>
    <!-- 底部数据展示区域 -->
    <DataTable
      class="px-5 mt-5"
      :columns="tableColumns"
      :data="tableData"
      height="calc(100vh - 730px)"
      @sort="handleSort"
    >
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRaw, useTemplateRef, watch } from 'vue'
import { NDatePicker } from 'naive-ui'

import { disablePreviousDate, formatTime } from '@/utils/tools/'
import { useUnifiedLoadStore, useCommonStore } from '@/stores'
import { applySorting } from '@/utils/sort'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/tables/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import DetailCard from '@/components/shared/cards/DetailCard.vue'

import {
  UnifiedLoadService,
  type UnifiedLoadDataItem,
  type UnifiedLoadStatisticItem,
} from '@/utils/api/services/load-forecasting/unifiedLoad'
import type { SeriesData } from '@/types'

// 默认表格列配置
const tableColumns = [
  {
    key: 'time',
    title: '时间',
    sorter: {},
  },
  {
    key: 'rtValue',
    title: '负荷实测',
    sorter: {},
  },
  {
    key: 'usPrediction',
    title: '负荷预测',
    sorter: {},
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
    sorter: {},
  },
]

const unifiedLoadStore = useUnifiedLoadStore()
const commonStore = useCommonStore()
const chartRef = useTemplateRef('chartRef')
const originalData = ref<UnifiedLoadStatisticItem>()
const tableData = ref<UnifiedLoadDataItem[]>([])

const chartData = computed<SeriesData[]>(() => {
  if (!originalData.value?.unifiedLfDataList.length) return []

  return [
    {
      name: '负荷预测',
      color: 'rgba(206, 66, 174, 1)',
      isShowAreaStyle: true,
      data: originalData.value?.unifiedLfDataList.map((item) => ({
        name: item.time,
        value: item.usPrediction,
      })),
    },
    {
      name: '负荷实测',
      color: 'rgba(12, 163, 159, 1)',
      isShowAreaStyle: true,
      data: originalData.value?.unifiedLfDataList.map((item) => ({
        name: item.time,
        value: item.rtValue,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: originalData.value?.unifiedLfDataList.map((item) => ({
        name: item.time,
        value: item.usDiff,
      })),
    },
  ]
})

const unifiedLoadCardData = computed(() => {
  return {
    name: {
      label: '日均偏差',
      value: originalData.value?.dailyAvgUsDiff,
    },
    volt: {
      label: '日均偏差率',
      value: originalData.value?.dailyAvgUsDiffRate,
    },
    time: {
      label: 'MAE',
      value: originalData.value?.mae,
    },
    maxFlow: {
      label: 'RMSE',
      value: originalData.value?.rmse,
    },
    maxValue: {
      label: 'MAPE',
      value: originalData.value?.mape,
    },
  }
})

const fetchUnifiedLoadData = async () => {
  commonStore.loading = true
  try {
    originalData.value = await UnifiedLoadService.getUnifiedLoadData({
      day: formatTime(unifiedLoadStore.timeRange[0], 'YYYY-MM-DD'),
    })
    tableData.value = JSON.parse(JSON.stringify(toRaw(originalData.value?.unifiedLfDataList)))
    // 处理响应数据
  } catch (error) {
    console.error('获取全省统调负荷数据失败:', error)
  } finally {
    commonStore.loading = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchUnifiedLoadData()
  unifiedLoadStore.isUnifiedLoadDetailVisible = true
}

const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    tableData.value = JSON.parse(JSON.stringify(toRaw(originalData.value?.unifiedLfDataList)))
    return
  }

  // 使用公共的多列排序函数
  tableData.value = applySorting(tableData.value, multiSortState, tableColumns)
}

watch(
  () => unifiedLoadStore.isUnifiedLoadDetailVisible,
  (val) => {
    //  都为false的时候，刷新lineChart大小
    if (!val) {
      chartRef.value?.resize()
    }
  },
)

onMounted(() => {
  fetchUnifiedLoadData()
})
</script>

<style></style>

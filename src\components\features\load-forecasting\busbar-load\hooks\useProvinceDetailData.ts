import { useBusbarLoadStore, useCommonStore } from '@/stores'
import type { SeriesData, TableColumn } from '@/types'
import {
  BusbarLoadService,
  type BusbarLoadDataDetailResponse,
  type BusbarLoadDetailQueryParams,
  type BusLfStatisticItem,
} from '@/utils/api/'
import { formatTime } from '@/utils/tools'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'

export const useProvinceDetailData = () => {
  // 默认表格列配置
  const tableColumns: TableColumn[] = [
    {
      key: 'time',
      title: '时间',
    },
    {
      key: 'sPrediction',
      title: '短期预测',
    },
    {
      key: 'usPrediction',
      title: '超短期预测',
    },
    {
      key: 'rtValue',
      title: '实时负荷',
    },
    {
      key: 'usDiff',
      title: '负荷偏差',
    },
    {
      key: 'usDiffRate',
      title: '负荷偏差率',
    },
  ]

  const busbarLoadStore = useBusbarLoadStore()
  const commonStore = useCommonStore()
  const selectedSortOption =
    ref<Exclude<keyof BusLfStatisticItem, 'busLfDataList'>>('dailyAvgUsDiff')
  const detailData = ref<BusbarLoadDataDetailResponse>({
    busLfDataList: [],
    busLfStatisticList: [],
  })

  const chartData = computed<SeriesData[]>(() => {
    if (!detailData.value.busLfDataList.length) return []
    // 获取 detailData.value中的busLfDataList中的数据 返回

    return [
      {
        name: '负荷预测',
        color: 'rgba(206, 66, 174, 1)',
        isShowAreaStyle: true,
        data: detailData.value.busLfDataList.map((item) => ({
          name: item.time,
          value: item.usPrediction,
        })),
      },
      {
        name: '负荷实测',
        color: 'rgba(12, 163, 159, 1)',
        isShowAreaStyle: true,
        data: detailData.value.busLfDataList.map((item) => ({
          name: item.time,
          value: item.rtValue,
        })),
      },
      {
        name: '负荷偏差',
        color: '#004EE4',
        yAxisIndex: 2,
        data: detailData.value.busLfDataList.map((item) => ({
          name: item.time,
          value: item.usDiff,
        })),
      },
    ]
  })

  const timeRange = computed(() => {
    if (!busbarLoadStore.timeRange.length) return ''
    return `${formatTime(busbarLoadStore.timeRange[0], 'YYYY-MM-DD')} ~ ${formatTime(busbarLoadStore.timeRange[1], 'YYYY-MM-DD')}`
  })

  const fetchBusbarLoadData = async () => {
    commonStore.loading = true
    try {
      const params: BusbarLoadDetailQueryParams = {}

      // 构建查询参数
      if (busbarLoadStore.timeRange[0]) {
        params.startDay = formatTime(busbarLoadStore.timeRange[0], 'YYYY-MM-DD')
      }
      if (busbarLoadStore.timeRange[1]) {
        params.endDay = formatTime(busbarLoadStore.timeRange[1], 'YYYY-MM-DD')
      }

      detailData.value = await BusbarLoadService.getBusbarLoadDataDetail(
        params,
        busbarLoadStore.currentType,
      )
      // 默认先排序
      handleSortOptionChange(selectedSortOption.value)
      // 处理响应数据
    } catch (error) {
      console.error('获取全省母线负荷数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  const handleSortOptionChange = (value: Exclude<keyof BusLfStatisticItem, 'busLfDataList'>) => {
    detailData.value.busLfStatisticList.sort((a, b) => {
      return parseFloat(b[value]) - parseFloat(a[value])
    })
  }

  const handleBack = () => {
    busbarLoadStore.isProvinceDetailVisible = false
    busbarLoadStore.isProvinceInfoVisible = false
  }

  onMounted(() => {
    fetchBusbarLoadData()
  })

  onBeforeUnmount(() => {
    busbarLoadStore.isProvinceDetailVisible = false
    busbarLoadStore.isProvinceInfoVisible = false
  })

  return {
    detailData,
    tableColumns,
    chartData,
    timeRange,
    selectedSortOption,
    handleSortOptionChange,
    handleBack,
  }
}

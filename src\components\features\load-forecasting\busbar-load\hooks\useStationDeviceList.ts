import { useBusbarLoadStore, useCommonStore, useSidebarStore } from '@/stores'
import { BusbarLoadService, type BusbarLoadExistDeviceListResponse } from '@/utils/api/'
import { formatTime, getTodayTimeRange } from '@/utils/tools'
import type { TreeSelectOption } from 'naive-ui'

/**
 * 查询母线负荷下的相关可查询设备列表
 * @returns
 */
export const useStationDeviceList = () => {
  const busbarLoadStore = useBusbarLoadStore()
  const sidebarStore = useSidebarStore()
  const commonStore = useCommonStore()

  const fetchStationDeviceList = async (
    timeRange = getTodayTimeRange(),
    regionCode?: string,
    fetchBothTypes = false,
  ) => {
    commonStore.loading = true
    try {
      if (fetchBothTypes) {
        // 同时获取 spot 和 plan 两种类型的数据（用于初始化侧边栏）
        const results = await Promise.allSettled([
          BusbarLoadService.getBusbarLoadExistDeviceList(
            {
              startTime: formatTime(timeRange[0], 'YYYY-MM-DD HH:mm:ss'),
              endTime: formatTime(timeRange[1], 'YYYY-MM-DD HH:mm:ss'),
              regionCode,
            },
            'spot',
          ),
          BusbarLoadService.getBusbarLoadExistDeviceList(
            {
              startTime: formatTime(timeRange[0], 'YYYY-MM-DD HH:mm:ss'),
              endTime: formatTime(timeRange[1], 'YYYY-MM-DD HH:mm:ss'),
              regionCode,
            },
            'plan',
          ),
        ])

        // 处理 spot 数据
        const spotResult = results[0]
        if (spotResult.status === 'fulfilled') {
          sidebarStore.setCityOptions(spotResult.value, regionCode, 'spot')
          // 如果当前类型是 spot，格式化数据到 busbarLoadStore
          if (busbarLoadStore.currentType === 'spot') {
            formatData(spotResult.value)
          }
        } else {
          sidebarStore.setCityOptions([], regionCode, 'spot')
          console.error('获取 spot 类型设备列表失败:', spotResult.reason)
        }

        // 处理 plan 数据
        const planResult = results[1]
        if (planResult.status === 'fulfilled') {
          sidebarStore.setCityOptions(planResult.value, regionCode, 'plan')
          // 如果当前类型是 plan，格式化数据到 busbarLoadStore
          if (busbarLoadStore.currentType === 'plan') {
            formatData(planResult.value)
          }
        } else {
          sidebarStore.setCityOptions([], regionCode, 'plan')
          console.error('获取 plan 类型设备列表失败:', planResult.reason)
        }
      } else {
        // 只获取当前类型的数据（默认行为）
        const response = await BusbarLoadService.getBusbarLoadExistDeviceList(
          {
            startTime: formatTime(timeRange[0], 'YYYY-MM-DD HH:mm:ss'),
            endTime: formatTime(timeRange[1], 'YYYY-MM-DD HH:mm:ss'),
            regionCode,
          },
          busbarLoadStore.currentType,
        )

        // 格式化数据到 busbarLoadStore
        formatData(response)

        // 只设置当前类型的城市选项
        sidebarStore.setCityOptions(response, regionCode, busbarLoadStore.currentType)
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  const formatData = (data: BusbarLoadExistDeviceListResponse[]) => {
    const list: TreeSelectOption[] = []
    data.forEach((item) => {
      const treeSelectOption = {
        ...item,
        label: item.name,
        key: item.id,
        stationId: item.id,
        children:
          item.deviceList.map((device) => ({
            ...device,
            label: device.name,
            key: device.id,
            deviceId: device.id,
          })) || [],
      }
      list.push(treeSelectOption)
    })
    busbarLoadStore.stationDeviceList = list
  }

  return {
    fetchStationDeviceList,
  }
}

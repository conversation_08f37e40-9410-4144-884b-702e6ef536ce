# API 封装使用说明

这是一个基于 axios 的完整 API 封装，提供了类型安全、错误处理、拦截器等功能。

## 目录结构

```
src/utils/api/
├── index.ts          # 统一导出
├── request.ts        # 核心请求类
├── instance.ts       # API实例配置
├── config.ts         # 配置文件
├── services/         # 具体业务API
│   ├── user.ts       # 用户相关API
│   ├── spotMarket.ts # 现货市场API
│   └── file.ts       # 文件相关API
├── examples.ts       # 使用示例
└── README.md         # 说明文档
```

## 快速开始

### 1. 基础使用

```typescript
import api from '@/utils/api'

// GET 请求
const response = await api.get('/users')

// POST 请求
const response = await api.post('/users', {
  name: 'John',
  email: '<EMAIL>',
})

// PUT 请求
const response = await api.put('/users/1', { name: '<PERSON>' })

// DELETE 请求
const response = await api.delete('/users/1')
```

### 2. 使用服务类

```typescript
import { UserService, SectionMonitoringService } from '@/utils/api'

// 用户登录
const loginResponse = await UserService.login({
  username: 'admin',
  password: '123456',
})

// 获取断面统计列表
const statisticList = await SectionMonitoringService.getSectionStatisticList({
  startTime: '2024-01-01T00:00:00',
  endTime: '2024-12-31T23:59:59',
})
```

### 3. 分页请求

```typescript
import api from '@/utils/api'

const response = await api.getPaginated('/data', {
  page: 1,
  pageSize: 10,
  keyword: 'search',
})

console.log(response.data.list) // 数据列表
console.log(response.data.total) // 总数
console.log(response.data.page) // 当前页
console.log(response.data.pageSize) // 页大小
```

### 4. 文件上传下载

```typescript
import { FileService } from '@/utils/api'

// 单文件上传
const file = document.querySelector('input[type="file"]').files[0]
const uploadResponse = await FileService.uploadFile(file)

// 文件下载
await FileService.downloadFile('file-id', 'filename.pdf')
```

## 配置

### 环境变量

在 `.env` 文件中配置：

```env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=现货市场QA系统
VITE_APP_VERSION=1.0.0
VITE_UPLOAD_URL=http://localhost:3000/upload
VITE_WEBSOCKET_URL=ws://localhost:3000/ws
```

### 请求配置

```typescript
import api from '@/utils/api'

const response = await api.get(
  '/data',
  {},
  {
    showLoading: true, // 显示loading
    showError: false, // 不显示错误提示
    retry: true, // 启用重试
    retryCount: 3, // 重试次数
    customErrorHandler: (error) => {
      // 自定义错误处理
      console.log('Custom error:', error)
    },
  },
)
```

## 错误处理

### 全局错误处理

系统会自动处理常见的HTTP错误：

- 400: 请求参数错误
- 401: 未授权，需要重新登录
- 403: 拒绝访问
- 404: 请求的资源不存在
- 500: 服务器内部错误

### 自定义错误处理

```typescript
try {
  const response = await api.get('/data')
} catch (error) {
  if (error.code === 401) {
    // 跳转到登录页
    router.push('/login')
  } else {
    // 显示错误信息
    message.error(error.message)
  }
}
```

## 拦截器

### 请求拦截器

自动添加：

- Authorization token
- 时间戳防缓存
- 请求日志

### 响应拦截器

自动处理：

- 统一响应格式
- 业务错误码
- 响应日志
- 401自动跳转

## 类型定义

所有API都有完整的TypeScript类型定义：

```typescript
import type { ApiResponse, PaginatedResponse } from '@/utils/api'

// 基础响应
interface ApiResponse<T> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp?: number
}

// 分页响应
interface PaginatedResponse<T> {
  code: number
  message: string
  data: {
    list: T[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
  success: boolean
}
```

## 最佳实践

### 1. 使用服务类

推荐为每个业务模块创建对应的服务类：

```typescript
export class UserService {
  static async getUsers() {
    return api.get<User[]>('/users')
  }

  static async createUser(user: CreateUserParams) {
    return api.post<User>('/users', user)
  }
}
```

### 2. 错误边界

在组件中使用try-catch处理错误：

```typescript
const handleSubmit = async () => {
  try {
    await UserService.createUser(formData)
    message.success('创建成功')
  } catch (error) {
    message.error(error.message)
  }
}
```

### 3. Loading状态

```typescript
const loading = ref(false)

const fetchData = async () => {
  loading.value = true
  try {
    const response = await api.get('/data')
    // 处理数据
  } finally {
    loading.value = false
  }
}
```

## 扩展

### 添加新的服务

1. 在 `services/` 目录下创建新文件
2. 定义相关类型
3. 创建服务类
4. 在 `index.ts` 中导出

### 自定义拦截器

```typescript
import { HttpRequest } from '@/utils/api'

const customApi = new HttpRequest({
  baseURL: '/custom-api',
  requestInterceptors: [
    {
      onFulfilled: (config) => {
        // 自定义请求处理
        return config
      },
    },
  ],
  responseInterceptors: [
    {
      onFulfilled: (response) => {
        // 自定义响应处理
        return response
      },
    },
  ],
})
```

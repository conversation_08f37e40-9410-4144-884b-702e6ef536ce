<template>
  <div class="flex flex-col">
    <!-- 表格容器 -->
    <div class="flex flex-1 bg-white">
      <!-- 表头 -->
      <div
        class="flex flex-col mr-2.5 rounded overflow-hidden text-16px leading-22px border border-solid border-#A9C0EA text-#6D84AE bg-#E4EBF8 w-33 px-3"
      >
        <div
          v-for="(column, index) in columns"
          :key="column.key"
          class="flex py-1 mb-1.75"
          :class="[{ 'flex-1': !column.height }, { 'items-center': index !== columns.length - 1 }]"
          :style="{ justifyContent: column.align || 'left', height: column.height }"
        >
          {{ column.title }}
        </div>
      </div>
      <!-- 表格内容 -->
      <!-- <n-scrollbar x-scrollable class="w-[calc(100vw-700px)] h-full flex"> -->
      <div class="flex overflow-x-auto v-scroll" :style="{ width }">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="flex flex-shrink-0 flex-col mr-2.5 rounded w-39.5"
        >
          <div
            v-for="(column, colIndex) in columns"
            :key="column.key"
            class="py-1 flex rounded text-20px bg-[#F8F8F8] not-last:mb-1.75 px-4"
            :class="[
              { 'flex-1': !column.height },
              { 'items-center': colIndex !== columns.length - 1 },
            ]"
            :style="{
              color: '#5F6673',
              justifyContent: column.align || 'left',
              height: column.height,
            }"
          >
            <slot
              :name="column.key"
              :item="item"
              :value="getColumnValue(item, column.key)"
              :index="index"
            >
              {{ getColumnValue(item, column.key) }}
            </slot>
          </div>
        </div>
      </div>
      <!-- </n-scrollbar> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TableColumn } from '@/types'

// 定义 props
interface Props {
  columns: TableColumn[]
  data: Record<string, any>[]
  height?: string
  width?: string
}

withDefaults(defineProps<Props>(), {
  height: '400px',
  width: '100%',
})

// 方法
const getColumnValue = (item: Record<string, any>, key: string) => {
  return item[key] || ''
}
</script>

<style scoped>
.v-scroll {
  overflow: auto;
  scrollbar-width: thin; /* Firefox */
}
</style>

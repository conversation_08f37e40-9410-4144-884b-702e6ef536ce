import { API_ENDPOINTS } from '../config'
import { api } from '../instance'

// 日报查询参数接口
export interface DailyReportQueryParams {
  day: string
}

export interface DailyReportQueryParamsFromNari {
  day: string
  statisticMode: number
}

// 调峰时段
export interface PeakingPeriodItem {
  startTime: string
  endTime: string
  type: string
  maxValue: string
}

//调峰详情
export interface PeakingDetailItem {
  stationName: string
  stationId: string
  value: string
}

//调峰时段详情
export interface PeakingTimeDetailItem {
  time: string
  type: string
  peakingDetailList: PeakingDetailItem[]
}

// 新能源数据 曲线列表
export interface NewEnergyDataItem {
  time: string
  totalPower: string
  totalPrediction: string
  windPower: string
  windPrediction: string
  pvPower: string
  pvPrediction: string
}

//  日报响应数据接口
export interface DailyReportResponse {
  description: string
  peakingPeriodList: PeakingPeriodItem[]
  peakingTimeDetailList: PeakingTimeDetailItem[]
  newEnergyDataList: NewEnergyDataItem[]
}
/*******日报v2 */

/**
 * 新能源数据 曲线列表v2
 * totalBeforePower	偏置前出清
 * totalAfterPower	偏置后出清
 * windBeforePower	偏置前出清
 * windAfterPower	偏置后出清
 * pvBeforePower	偏置前出清
 * pvAfterPower	偏置后出清
 * windMarketstring 风电市场出清
 * pvMarketstring 光伏市场出清
 * totalMarketstring 总市场出清
 */
export type newEnergyDataItemV2 = NewEnergyDataItem & {
  totalBeforePower: string
  totalAfterPower: string
  windBeforePower: string
  windAfterPower: string
  pvBeforePower: string
  pvAfterPower: string
  windMarketstring: string
  pvMarketstring: string
  totalMarketstring: string
}

/**
 * 风电/光伏机组列表
 * generatorName	机组名称
 * maxValue	最大调峰电力
 * maxTime	最大调峰时刻
 * totalPower	调峰电量
 * period	调峰时段
 * judgment	调峰判据
 */
export interface WindPVGeneratorDetailItem {
  generatorName: string
  maxValue: string
  maxTime: string
  totalPower: string
  period: string
  judgment: string
}

export interface DailyReportResponseFromNari {
  description: string
  peakingPeriodList: PeakingPeriodItem[]
  newEnergyDataList: newEnergyDataItemV2[]
  windGeneratorDetailList: WindPVGeneratorDetailItem[]
  pvGeneratorDetailList: WindPVGeneratorDetailItem[]
}

//  日报服务类
export class DailyReportService {
  /**
   *  获取日报数据
   * @param params 查询参数
   * @returns  日报数据
   */
  static async getDailyReportData(params: DailyReportQueryParams): Promise<DailyReportResponse> {
    return api.get<DailyReportResponse>(API_ENDPOINTS.DAILY_REPORT.DAILY_REPORT_DATA, params)
  }

  /**
   * 获取新能源日报接口(南瑞数据源)
   * @param params
   * @returns
   */
  static async getDailyReportDataFromNari(params: DailyReportQueryParamsFromNari) {
    return api.get<DailyReportResponseFromNari>(
      API_ENDPOINTS.DAILY_REPORT.DAILY_REPORT_DATA_FROM_NARI,
      params,
    )
  }
}

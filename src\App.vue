<template>
  <n-config-provider
    :locale="zhCN"
    :date-locale="dateZhCN"
    :theme-overrides="themeOverrides"
    :theme="null"
  >
    <n-message-provider>
      <RouterView />
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { themeOverrides } from './utils/theme'
import { NConfigProvider, zhCN, dateZhCN, NMessageProvider } from 'naive-ui'
</script>

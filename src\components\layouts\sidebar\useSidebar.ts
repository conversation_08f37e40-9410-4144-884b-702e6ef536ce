import { NMenu, type MenuOption } from 'naive-ui'

import { nextTick, onMounted, ref, useTemplateRef, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStationDeviceList } from '@/components/features/load-forecasting/busbar-load/hooks/'

export const useSidebar = () => {
  const router = useRouter()
  const route = useRoute()

  const menuRef = useTemplateRef<InstanceType<typeof NMenu>>('menuRef')

  const activeKey = ref('daily-report')

  watch(
    () => router.currentRoute.value,
    (route) => {
      const { query, name } = route
      activeKey.value = query.key ? (query.key as string) : (name as string)

      // 确保 DOM 初始化后再调用
      nextTick(() => {
        menuRef.value?.showOption(activeKey.value)
      })
    },
    { immediate: true },
  )
  /**
   * 如果item.route有值，则将key作为route的参数 ?key=
   * @param key
   * @param item
   */
  // TODO 动态获取当前地市下的设备后，无法展开地市
  const handleMenuSelect = (key: string, item: MenuOption) => {
    console.log('🚀 ~ handleMenuSelect ~ key:', key)
    activeKey.value = key
    if (item.route) {
      const queryParams: Record<string, string> = {
        key,
      }

      if (item.regionCode) {
        queryParams['regionCode'] = item.regionCode as string
      }
      if (item.stationId) {
        queryParams['stationId'] = item.stationId as string
      }
      if (item.deviceId) {
        queryParams['deviceId'] = item.deviceId as string
      }
      if (item.label || item.name) {
        queryParams['name'] = (item.name as string) || (item.label as string)
      }
      if (item.type) {
        queryParams['type'] = item.type as string
      }
      // 保留时间范围
      if (item.route === '/load-forecasting/busbar-load' && route.query.timeRange) {
        queryParams['timeRange'] = route.query.timeRange as string
      }
      router.push({
        path: item.route as string,
        query: queryParams,
      })
    } else {
      router.push({
        name: key,
      })
    }
  }

  const { fetchStationDeviceList } = useStationDeviceList()

  onMounted(async () => {
    // const timeRange = route.query.timeRange
    // 默认获取当天数据，同时获取 spot 和 plan 两种类型用于侧边栏初始化
    await fetchStationDeviceList(undefined, undefined, true)
    await nextTick()
    menuRef.value?.showOption(activeKey.value)
  })
  return {
    activeKey,
    handleMenuSelect,
  }
}

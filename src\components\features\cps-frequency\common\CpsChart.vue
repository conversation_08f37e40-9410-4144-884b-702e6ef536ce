<template>
  <LineChart
    ref="chartRef"
    class="mt-4"
    title="CPS1、CPS2全天及江苏频率曲线"
    :data="chartData"
    height="400px"
    :custom-config="customConfig"
  />
</template>

<script setup lang="ts">
import LineChart from '@/components/shared/charts/LineChart.vue'
import { useCpsFrequencyStore } from '@/stores'
import type { SeriesData } from '@/types'
import { computed, onMounted, useTemplateRef } from 'vue'

const cpsFrequencyStore = useCpsFrequencyStore()
const chartRef = useTemplateRef('chartRef')

const customConfig = {
  legend: {
    icon: undefined,
    itemHeight: 2,
  },
}

// 图表数据
const chartData = computed<SeriesData[]>(() => {
  if (!cpsFrequencyStore.cpsData) return []

  return [
    {
      name: '全天CPS1',
      color: '#3CB670',
      symbol: 'emptyCircle',
      data: cpsFrequencyStore.cpsData.CPS1List.map((item) => ({
        name: item.time,
        value: item.value,
      })),
    },
    {
      name: '全天CPS2',
      color: '#2585E6',
      symbol: 'emptyCircle',
      data: cpsFrequencyStore.cpsData.CPS2List.map((item) => ({
        name: item.time,
        value: item.value,
      })),
    },
    {
      name: '频率',
      color: '#D240A6',
      yAxisIndex: 1,
      symbol: 'emptyCircle',
      data: cpsFrequencyStore.cpsData.JSRateList.map((item) => ({
        name: item.time,
        value: item.value,
      })),
    },
  ]
})

onMounted(() => {
  cpsFrequencyStore.chartRef = chartRef.value
})
</script>

<style scoped></style>

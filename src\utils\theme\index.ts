import type { GlobalThemeOverrides } from 'naive-ui'

const primaryColor = '#3075F6FF'
const primaryColorHover = '#82ABF8FF'
const primaryColorPressed = '#5E85CFFF'
const primaryColorSuppl = '#284D92FF'

const whiteColor = '#FFFFFF'

export const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: primaryColor,
    primaryColorHover: primaryColorHover,
    primaryColorPressed: primaryColorPressed,
    primaryColorSuppl: primaryColorSuppl,
    successColor: primaryColorPressed,
    successColorHover: primaryColorHover,
    successColorPressed: primaryColorPressed,
    successColorSuppl: primaryColorSuppl,
  },
  Button: {
    colorPrimary: '#E2ECFFFF',
    textColorPrimary: primaryColor,
    colorHoverPrimary: primaryColor,

    borderHoverPrimary: `1px solid ${primaryColor}`,
  },
  Menu: {
    color: primaryColor,
    itemColorActive: '#1958CE ',
    itemColorActiveHover: '#1958CE ',
    itemColorHover: '#1958CE ',
    itemTextColor: whiteColor,
    itemTextColorHover: whiteColor,
    itemTextColorActive: whiteColor,
    itemTextColorActiveHover: whiteColor,
    itemTextColorChildActive: whiteColor,
    itemTextColorChildActiveHover: whiteColor,
    itemIconColorActive: whiteColor,
    itemIconColorActiveHover: whiteColor,
    itemIconColorChildActive: whiteColor,
    itemIconColorChildActiveHover: whiteColor,
    arrowColor: whiteColor,
    arrowColorHover: whiteColor,
    arrowColorActive: whiteColor,
    arrowColorChildActive: whiteColor,
    arrowColorChildActiveHover: whiteColor,

    // itemHeight: '54px',
    // fontSize: '18px',
  },
  DataTable: {
    thPaddingLarge: '10.5px',
    fontSizeLarge: '16px',
    tdPaddingLarge: '10px',
    tdColorHover: '#F4DBDB',
    thColor: '#A9C0EA',
    thColorSorting: 'none',
    tdColor: '#F8F8F8',
    tdColorSorting: 'none',
  },
  Input: {
    groupLabelColor: whiteColor,
    groupLabelTextColor: '#A1A9B7',
  },
}

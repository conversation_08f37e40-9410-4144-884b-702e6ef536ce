<template>
  <n-button type="info" size="large" @click="handleBack">
    <template #icon>
      <n-icon><ChevronLeft20FilledIcon /></n-icon>
    </template>
    返回
  </n-button>
</template>

<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui'
import { ChevronLeft20FilledIcon } from '@/utils/constant/icons'

const emit = defineEmits<{
  click: []
}>()

const handleBack = () => {
  emit('click')
}
</script>

<style scoped></style>

import { API_ENDPOINTS } from '../../config'
import { api } from '../../instance'
import type { BusLfDataItem, BusLfStatisticItem } from './busbarLoad'

export interface UnifiedLoadDetailQueryParams {
  startDay?: string
  endDay?: string
}

export type UnifiedLoadDataItem = BusLfDataItem
// 统调负荷曲线信息
export type UnifiedLoadStatisticItem = Omit<BusLfStatisticItem, 'name' | 'busLfDataList'> & {
  unifiedLfDataList: UnifiedLoadDataItem[]
}

// 统调负荷曲线多日详情
export interface UnifiedLoadDataDetailItem {
  unifiedLfDataList: UnifiedLoadDataItem[]
  unifiedStatisticList: UnifiedLoadStatisticItem[]
}

// 统调负荷曲线月度详情
export interface UnifiedLoadDataMonthlyDetailItem {
  unifiedLfDataList: UnifiedLoadDataItem[]
  monthlyStatisticList: UnifiedLoadStatisticItem[]
  dailyStatisticList: UnifiedLoadStatisticItem[]
}

export class UnifiedLoadService {
  /**
   *  获取统调负荷曲线信息
   * @param params
   * @returns
   */
  static async getUnifiedLoadData(params: { day: string }) {
    return api.get<UnifiedLoadStatisticItem>(
      API_ENDPOINTS.LOAD_FORECASTING.UNIFIED_LOAD_DATA,
      params,
    )
  }

  /**
   *  获取统调负荷曲线多日详情
   * @param params
   * @returns
   */
  static async getUnifiedLoadDataDetail(params: UnifiedLoadDetailQueryParams) {
    return api.get<UnifiedLoadDataDetailItem>(
      API_ENDPOINTS.LOAD_FORECASTING.UNIFIED_LOAD_DATA_DETAIL,
      params,
    )
  }

  /**
   *  获取统调负荷曲线月度详情
   * @param params
   * @returns
   */
  static async getUnifiedLoadDataMonthlyDetail(params: UnifiedLoadDetailQueryParams) {
    return api.get<UnifiedLoadDataMonthlyDetailItem>(
      API_ENDPOINTS.LOAD_FORECASTING.UNIFIED_LOAD_DATA_MONTHLY_DETAIL,
      params,
    )
  }
}

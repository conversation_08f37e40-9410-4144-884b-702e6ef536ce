# Peak Shaving Export Hook

## 概述

`usePeakShavingExport` 是一个用于导出新能源弃电情况统计数据的 Vue 3 Composition API hook。它支持导出四个图表和一个表格的数据到 Excel 文件。

## 功能特性

- 📊 支持四个图表的数据导出：
  - 新能源弃电情况统计
  - 风力发电
  - 光伏发电
  - 断面曲线
- 📋 支持当日越限断面表格数据导出
- 🖼️ 自动获取图表图片并嵌入到导出文件中
- 📝 提供详细的数据统计信息和元数据
- 🎨 支持自定义样式和格式

## 使用方法

### 1. 导入 Hook

```typescript
import { usePeakShavingExport } from './hooks/usePeakShavingExport'
```

### 2. 在组件中使用

```typescript
// 获取图表引用
const statisticChartRef = useTemplateRef('statisticChartRef')
const windChartRef = useTemplateRef('windChartRef')
const solarChartRef = useTemplateRef('solarChartRef')
const sectionChartRef = useTemplateRef('sectionChartRef')

// 导出数据状态
const dataByExport = ref<ExportBlockOptions>()

// 使用 hook
const { getExportData } = usePeakShavingExport()

// 处理导出
const handleExport = () => {
  dataByExport.value = getExportData({
    statisticChartRef: statisticChartRef.value,
    windChartRef: windChartRef.value,
    solarChartRef: solarChartRef.value,
    sectionChartRef: sectionChartRef.value,
    tableData: peakShavingData.value,
    date: formatTime(time.value, 'YYYY-MM-DD')
  })
}
```

### 3. 在模板中使用

```vue
<ExportButton
  class="mr-2.5"
  :filename="filename"
  export-type="excel-structured"
  @export-start="handleExport"
  :structured-data="dataByExport"
/>
```

## 数据结构

### ExportParams 接口

```typescript
interface ExportParams {
  statisticChartRef: InstanceType<typeof LineChart> | null
  windChartRef: InstanceType<typeof LineChart> | null
  solarChartRef: InstanceType<typeof LineChart> | null
  sectionChartRef: InstanceType<typeof LineChart> | null
  tableData: SectionStatisticData[]
  date: string
}
```

### 导出数据格式

导出的 Excel 文件包含以下部分：

1. **概览信息**
   - 日期
   - 报告类型
   - 数据来源统计

2. **当日越限断面统计**
   - 统计信息（总数量、时间、类型）
   - 断面名称
   - 电压等级
   - 最大潮流
   - 最大潮流越限出现时间

3. **图表图片**
   - 自动选择最佳可用图表作为封面图片

## 特性说明

- **智能图片选择**: 按优先级自动选择图表图片（新能源弃电情况统计 > 风力发电 > 光伏发电 > 断面曲线）
- **空数据处理**: 当没有表格数据时，会显示友好的提示信息
- **样式支持**: 支持 Excel 单元格样式，包括背景色、字体样式等
- **类型安全**: 完整的 TypeScript 类型定义

## 依赖

- `@/components/shared/charts/LineChart.vue`
- `@/utils/api` (SectionStatisticData 类型)
- `@/utils/export` (ExportBlockOptions 类型)

## 注意事项

- 确保图表组件已正确挂载并具有 `getImg()` 方法
- 表格数据应符合 `SectionStatisticData[]` 类型定义
- 导出功能依赖于 ExportButton 组件的 `excel-structured` 导出类型

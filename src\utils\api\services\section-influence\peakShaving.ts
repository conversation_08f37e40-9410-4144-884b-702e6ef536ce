import api from '../../instance'
import { API_ENDPOINTS } from '../../config'
import type { DailyReportQueryParamsFromNari, DailyReportResponseFromNari } from '../dailyReport'
import type { SectionStatisticData, SectionStatisticQueryParams } from './sectionMonitoring'

/**
 * 断面数据项
 */
export interface SectionDataItem {
  sectionId: string
  sectionName: string
  volt: string
}

/**
 * 断面曲线数据点
 */
export interface SectionCurveDataPoint {
  time: string
  value: string
  diffValue: string
  limit: string
  marketValue: string
  planValue: string
}

/**
 * 断面曲线查询参数
 */
export interface SectionCurveQueryParams {
  sectionId: string
  day: string
}

/**
 * 断面影响调峰情况服务类
 */
export class PeakShavingService {
  // getNewEnergyDailyStatisticFromNari 日报v2接口
  static async getPeakShavingChartData(params: DailyReportQueryParamsFromNari) {
    return api.get<DailyReportResponseFromNari>(
      API_ENDPOINTS.DAILY_REPORT.DAILY_REPORT_DATA_FROM_NARI,
      params,
    )
  }

  // 表格使用/getSectionStatisticList 接口
  static async getPeakShavingDataList(params: SectionStatisticQueryParams) {
    return api.get<SectionStatisticData[]>(
      API_ENDPOINTS.SECTION_STATISTIC.SECTION_STATISTIC_LIST,
      params,
    )
  }

  /**
   * 获取所有断面数据
   */
  static async getAllSectionDataList() {
    return api.get<SectionDataItem[]>(API_ENDPOINTS.SECTION_STATISTIC.ALL_SECTION_DATA)
  }

  /**
   * 获取断面曲线数据（包含计划和市场值）
   */
  static async getSectionDataWithMarketAndPlan(params: SectionCurveQueryParams) {
    return api.get<SectionCurveDataPoint[]>(
      API_ENDPOINTS.SECTION_STATISTIC.SECTION_DATA_WITH_MARKET_AND_PLAN,
      params,
    )
  }
}

<template>
  <KeepAlive :include="['UnifiedLoadStatistic']">
    <component :is="components"></component>
  </KeepAlive>
</template>

<script setup lang="ts">
import UnifiedLoadStatistic from '@/components/features/load-forecasting/unified-load/UnifiedLoadStatistic.vue'
import UnifiedLoadDetail from '@/components/features/load-forecasting/unified-load/UnifiedLoadDetail.vue'

import { useUnifiedLoadStore } from '@/stores'
import { computed } from 'vue'

const unifiedLoadStore = useUnifiedLoadStore()

const components = computed(() => {
  if (unifiedLoadStore.isUnifiedLoadDetailVisible) {
    return UnifiedLoadDetail
  }
  return UnifiedLoadStatistic
})
</script>

<style scoped></style>

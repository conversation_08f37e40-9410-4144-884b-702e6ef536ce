<template>
  <div class="flex flex-col h-full">
    <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
      <div class="flex flex-1 text-2xl text-#9E9E9E">{{ timeRangeStr }}</div>
      <div class="flex">
        <ExportButton
          class="mr-2.5"
          :filename="filename"
          export-type="excel-structured"
          @export-start="handleExport"
          :structured-data="dataByExport"
        />
        <BackButton @click="handleBack"></BackButton>
      </div>
    </div>

    <LineChart ref="chartRef" :data="chartData" yAxisName="万千瓦" height="450px" class="mt-2" />

    <VerticalTable
      v-if="isCrossMonth"
      class="flex-1 px-5"
      :columns="tableMonthlyColumns"
      :data="unifiedLoadDataMonthlyDetailData.monthlyStatisticList"
    ></VerticalTable>

    <n-select
      v-model:value="selectedSortOption"
      class="w-42.5 self-end mb-2 mr-3"
      :options="SortOptions"
    ></n-select>

    <n-scrollbar style="height: calc(100vh - 580px)">
      <div v-for="(item, index) in tableDailyList" :key="index" class="flex flex-col pl-5 mb-5">
        <div class="flex">
          <LoadDetailCard class="w-50" :data="item" isGreenStyle />
          <div class="flex px-5">
            <!-- 横向表格数据 -->
            <VerticalTable
              class="flex-1"
              width="calc(100vw - 755px)"
              :columns="tableDailyColumns"
              :data="item.unifiedLfDataList"
            ></VerticalTable>
          </div>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, useTemplateRef, watch } from 'vue'

import { formatTime, isTimeRangeCrossMonth } from '@/utils/tools/'
import { useCommonStore, useUnifiedLoadStore } from '@/stores'

import { NSelect, NScrollbar } from 'naive-ui'

import LineChart from '@/components/shared/charts/LineChart.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'
import BackButton from '@/components/shared/buttons/BackButton.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import LoadDetailCard from '@/components/features/load-forecasting/common/LoadDetailCard.vue'

import {
  UnifiedLoadService,
  type UnifiedLoadDataDetailItem,
  type UnifiedLoadDataMonthlyDetailItem,
  type UnifiedLoadStatisticItem,
} from '@/utils/api/services/load-forecasting/unifiedLoad'
import type { SeriesData } from '@/types'
import { SortOptions } from '@/utils/constant/options'
import type { ExportBlockOptions } from '@/utils/export'
import { useUnifiedLoadDetailExport } from './useUnifiedLoadDetailExport'

const tableMonthlyColumns = [
  {
    key: 'date',
    title: '月份',
  },
  {
    key: 'dailyAvgUsDiff',
    title: '月份偏差',
  },
  {
    key: 'dailyAvgUsDiffRate',
    title: '月份偏差率',
  },
  {
    key: 'mae',
    title: 'MAE',
  },
  {
    key: 'rmse',
    title: 'RMSE',
  },
  {
    key: 'mape',
    title: 'MAPE',
  },
]

const tableDailyColumns = [
  {
    key: 'time',
    title: '时间',
  },
  {
    key: 'usPrediction',
    title: '负荷预测',
  },
  {
    key: 'rtValue',
    title: '负荷实测',
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
  },
]
const unifiedLoadStore = useUnifiedLoadStore()

const commonStore = useCommonStore()
const unifiedLoadDataDetailData = ref<UnifiedLoadDataDetailItem>({
  unifiedLfDataList: [],
  unifiedStatisticList: [],
})

const unifiedLoadDataMonthlyDetailData = ref<UnifiedLoadDataMonthlyDetailItem>({
  unifiedLfDataList: [],
  monthlyStatisticList: [],
  dailyStatisticList: [],
})

const selectedSortOption =
  ref<Exclude<keyof UnifiedLoadStatisticItem, 'unifiedLfDataList'>>('dailyAvgUsDiff')

const timeRangeStr = computed(() => {
  return `${formatTime(unifiedLoadStore.timeRange[0], 'YYYY-MM-DD')} ~ ${formatTime(
    unifiedLoadStore.timeRange[1],
    'YYYY-MM-DD',
  )}`
})

const isCrossMonth = computed(() => {
  return isTimeRangeCrossMonth(unifiedLoadStore.timeRange)
})

const tableDailyList = computed(() => {
  const list = isCrossMonth.value
    ? unifiedLoadDataMonthlyDetailData.value.dailyStatisticList
    : unifiedLoadDataDetailData.value.unifiedStatisticList

  list.sort((a, b) => {
    return parseFloat(b[selectedSortOption.value]) - parseFloat(a[selectedSortOption.value])
  })

  return list
})

const chartData = computed<SeriesData[]>(() => {
  const chartOriginData = isCrossMonth.value
    ? unifiedLoadDataMonthlyDetailData.value.unifiedLfDataList
    : unifiedLoadDataDetailData.value.unifiedLfDataList

  return [
    {
      name: '负荷预测',
      color: 'rgba(206, 66, 174, 1)',
      isShowAreaStyle: true,
      data: chartOriginData.map((item) => ({
        name: item.time,
        value: item.usPrediction,
      })),
    },
    {
      name: '负荷实测',
      color: 'rgba(12, 163, 159, 1)',
      isShowAreaStyle: true,
      data: chartOriginData.map((item) => ({
        name: item.time,
        value: item.rtValue,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: chartOriginData.map((item) => ({
        name: item.time,
        value: item.usDiff,
      })),
    },
  ]
})

const fetchBusbarLoadData = async () => {
  commonStore.loading = true
  try {
    unifiedLoadDataDetailData.value = await UnifiedLoadService.getUnifiedLoadDataDetail({
      startDay: formatTime(unifiedLoadStore.timeRange[0], 'YYYY-MM-DD'),
      endDay: formatTime(unifiedLoadStore.timeRange[1], 'YYYY-MM-DD'),
    })
    // 处理响应数据
  } catch (error) {
    console.error('获取全省统调负荷数据失败:', error)
  } finally {
    await nextTick()
    commonStore.loading = false
  }
}
// getUnifiedLoadDataMonthlyDetail
const fetchBusbarLoadDataMonthly = async () => {
  commonStore.loading = true
  try {
    unifiedLoadDataMonthlyDetailData.value =
      await UnifiedLoadService.getUnifiedLoadDataMonthlyDetail({
        startDay: formatTime(unifiedLoadStore.timeRange[0], 'YYYY-MM-DD'),
        endDay: formatTime(unifiedLoadStore.timeRange[1], 'YYYY-MM-DD'),
      })
    // 处理响应数据
  } catch (error) {
    console.error('获取全省统调负荷数据失败:', error)
  } finally {
    await nextTick()
    commonStore.loading = false
  }
}

const handleBack = () => {
  unifiedLoadStore.isUnifiedLoadDetailVisible = false
}

watch(
  () => isCrossMonth.value,
  (val) => {
    if (val) {
      fetchBusbarLoadDataMonthly()
    } else {
      fetchBusbarLoadData()
    }
  },
  { immediate: true },
)
/*******导出 */
const chartRef = useTemplateRef('chartRef')
const dataByExport = ref<ExportBlockOptions>()

const filename = computed(() => {
  return `全省统调负荷数据详情_${formatTime(unifiedLoadStore.timeRange[0], 'YYYY-MM-DD')}_${formatTime(
    unifiedLoadStore.timeRange[1],
    'YYYY-MM-DD',
  )}`
})

const { getExportData } = useUnifiedLoadDetailExport()
const handleExport = () => {
  let data = unifiedLoadDataDetailData.value.unifiedStatisticList
  if (isCrossMonth.value) {
    const { dailyStatisticList, monthlyStatisticList } = unifiedLoadDataMonthlyDetailData.value
    data = monthlyStatisticList.concat(dailyStatisticList)
  }
  dataByExport.value = getExportData(data, chartRef.value)
}
</script>

<style></style>

/**
 * 排序工具函数
 */
import dayjs from 'dayjs'
import { isFullDate, isTimeOnly, parseTime } from '../tools'

/**
 * 断面统计数据类型
 */
export interface SectionStatisticData {
  id?: string
  name?: string
  volt?: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime?: string
  time?: string
  overPeriod?: string
}

/**
 * 电压等级排序权重
 */
const VOLT_ORDER: { [key: string]: number } = {
  '500KV': 3,
  '220KV': 2,
  '110KV': 1,
}

/**
 * 默认排序函数 - 按照 电压等级>总越限时间>差额最大值>最大潮流>名字首字母 的优先级排序
 * @param data 要排序的数据数组
 * @returns 排序后的数据数组
 */
export const applyDefaultSort = <T extends SectionStatisticData>(data?: T[]): T[] => {
  if (!data) return []
  return [...data].sort((a, b) => {
    // 1. 电压等级排序（最高优先级）
    if (a.volt && b.volt) {
      const aVolt = VOLT_ORDER[a.volt] || 0
      const bVolt = VOLT_ORDER[b.volt] || 0
      if (aVolt !== bVolt) {
        return bVolt - aVolt // 降序，高电压优先
      }
    }

    // 2. 总越限时间排序
    if (a.totalOverTime && b.totalOverTime) {
      const aTotalOverTime = parseFloat(a.totalOverTime) || 0
      const bTotalOverTime = parseFloat(b.totalOverTime) || 0
      if (aTotalOverTime !== bTotalOverTime) {
        return bTotalOverTime - aTotalOverTime // 降序
      }
    }

    // 3. 差额最大值排序
    if (a.maxDiffValue && b.maxDiffValue) {
      const aMaxDiffValue = parseFloat(a.maxDiffValue) || 0
      const bMaxDiffValue = parseFloat(b.maxDiffValue) || 0
      if (aMaxDiffValue !== bMaxDiffValue) {
        return bMaxDiffValue - aMaxDiffValue // 降序
      }
    }

    // 4. 最大潮流排序
    if (a.maxValue && b.maxValue) {
      const aMaxValue = parseFloat(a.maxValue) || 0
      const bMaxValue = parseFloat(b.maxValue) || 0
      if (aMaxValue !== bMaxValue) {
        return bMaxValue - aMaxValue // 降序
      }
    }

    // 5. 名字首字母排序（最低优先级）
    const aName = String(a.name || '')
    const bName = String(b.name || '')
    if (aName && bName) {
      return aName.localeCompare(bName, 'zh-CN', { numeric: true }) // 升序
    }

    // 6. 时间排序（详情页面特有）
    if (a.time && b.time) {
      return new Date(a.time).getTime() - new Date(b.time).getTime() // 升序
    }

    return 0
  })
}

/**
 * 电压等级比较函数
 * @param a 第一个数据项
 * @param b 第二个数据项
 * @returns 比较结果
 */
export const compareVoltage = (a: any, b: any): number => {
  const aVolt = VOLT_ORDER[a.volt] || 0
  const bVolt = VOLT_ORDER[b.volt] || 0
  return bVolt - aVolt // 降序，高电压优先
}

/**
 * 数值字段比较函数（降序）
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareNumericDesc = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aNum = parseFloat(a[fieldName]) || 0
    const bNum = parseFloat(b[fieldName]) || 0
    return bNum - aNum // 降序
  }
}

/**
 * 数值字段比较函数（升序）
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareNumericAsc = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aNum = parseFloat(a[fieldName]) || 0
    const bNum = parseFloat(b[fieldName]) || 0
    return aNum - bNum // 升序
  }
}

/**
 * 字符串字段比较函数
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareString = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aStr = String(a[fieldName] || '')
    const bStr = String(b[fieldName] || '')
    return aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
  }
}

/**
 * 时间字段比较函数
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareTime = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aTime = new Date(a[fieldName]).getTime()
    const bTime = new Date(b[fieldName]).getTime()
    return aTime - bTime // 升序
  }
}

/**
 * 排序状态接口
 */
export interface SortState {
  order: 'asc' | 'desc'
  multiple?: number
  clickOrder?: number // 点击顺序，用于没有设置 multiple 时的排序
}

/**
 * 排序列配置接口
 */
export interface SortColumn {
  key: string
  order: 'asc' | 'desc'
  priority: number // 排序优先级，数值越大优先级越高
  column?: any
}

/**
 * 通用多列排序处理函数
 * @param data 要排序的数据
 * @param sortState 排序状态对象，key为列名，value为排序状态
 * @param columns 列配置数组（可选）
 * @returns 排序后的数据
 */
export const applySorting = <T extends Record<string, any> = any>(
  data: T[],
  sortState: Record<string, SortState>,
  columns?: any[],
): T[] => {
  if (Object.keys(sortState).length === 0) {
    return data
  }

  // 构建排序列配置
  const sortColumns: SortColumn[] = Object.entries(sortState)
    .map(([key, state]: [string, SortState]) => {
      // 优先使用 multiple 作为排序优先级，如果没有则使用 clickOrder
      // 数值越大优先级越高，所以需要取负值让最新点击的排在前面
      let priority = 0
      if (typeof state.multiple === 'number') {
        priority = state.multiple
      } else if (typeof state.clickOrder === 'number') {
        // clickOrder 越大表示越晚点击，优先级应该越高
        priority = state.clickOrder
      }

      return {
        key,
        order: state.order,
        priority,
        column: columns?.find((col: any) => col.key === key),
      }
    })
    // 按优先级降序排列，优先级高的先排序
    .sort((a, b) => b.priority - a.priority)

  return [...data].sort((a, b) => {
    for (const sortCol of sortColumns) {
      let result = 0

      if (
        sortCol.column?.sorter &&
        typeof sortCol.column.sorter === 'object' &&
        sortCol.column.sorter.compare
      ) {
        // 如果列有自定义比较函数，使用它
        result = sortCol.column.sorter.compare(a, b)
      } else {
        // 使用通用比较逻辑
        result = compareValues(a[sortCol.key], b[sortCol.key])
      }

      if (sortCol.order === 'desc') {
        result = -result
      }

      if (result !== 0) {
        return result
      }
    }

    return 0
  })
}

/**
 * 通用值比较函数
 * @param aValue 第一个值
 * @param bValue 第二个值
 * @returns 比较结果
 */
export const compareValues = (aValue: any, bValue: any): number => {
  // 处理 null/undefined
  if (aValue == null && bValue == null) return 0
  if (aValue == null) return -1
  if (bValue == null) return 1

  const aStr = String(aValue).trim()
  const bStr = String(bValue).trim()

  // 日期或日期时间比较
  if (isFullDate(aStr) && isFullDate(bStr)) {
    const aDate = dayjs(aStr)
    const bDate = dayjs(bStr)
    if (aDate.isValid() && bDate.isValid()) {
      return aDate.isBefore(bDate) ? -1 : aDate.isAfter(bDate) ? 1 : 0
    }
  }

  // 时间字符串比较（当天时间拼接）
  if (isTimeOnly(aStr) && isTimeOnly(bStr)) {
    const aTime = parseTime(aStr)
    const bTime = parseTime(bStr)
    if (aTime.isValid() && bTime.isValid()) {
      return aTime.isBefore(bTime) ? -1 : aTime.isAfter(bTime) ? 1 : 0
    }
  }

  // 数值比较
  const aNum = parseFloat(aValue)
  const bNum = parseFloat(bValue)
  const aNumValid = !isNaN(aNum)
  const bNumValid = !isNaN(bNum)

  if (aNumValid && bNumValid) {
    // 其次处理数值
    return aNum - bNum
  } else {
    // 最后默认使用字符串比较
    const aStr = String(aValue ?? '')
    const bStr = String(bValue ?? '')
    return aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
  }
}

/**
 * 更新排序状态，自动管理点击顺序
 * @param currentSortState 当前排序状态
 * @param columnKey 要更新的列键
 * @param newOrder 新的排序方向，如果不提供则自动切换
 * @param useMultiple 是否使用 multiple 属性而不是 clickOrder
 * @returns 更新后的排序状态
 */
export const updateSortState = (
  currentSortState: Record<string, SortState>,
  columnKey: string,
  newOrder?: 'asc' | 'desc',
  useMultiple = false,
): Record<string, SortState> => {
  const newState = { ...currentSortState }

  // 如果列已存在，获取当前状态
  const currentState = newState[columnKey]

  // 确定新的排序方向
  let order: 'asc' | 'desc'
  if (newOrder) {
    order = newOrder
  } else if (currentState) {
    // 切换排序方向：asc -> desc -> 移除
    if (currentState.order === 'asc') {
      order = 'desc'
    } else {
      // 如果是 desc，则移除该列的排序
      delete newState[columnKey]
      return newState
    }
  } else {
    // 新列默认升序
    order = 'asc'
  }

  if (useMultiple) {
    // 使用 multiple 属性管理优先级
    const maxMultiple = Math.max(0, ...Object.values(newState).map((state) => state.multiple || 0))
    newState[columnKey] = {
      order,
      multiple: maxMultiple + 1,
    }
  } else {
    // 使用 clickOrder 属性管理优先级
    const maxClickOrder = Math.max(
      0,
      ...Object.values(newState).map((state) => state.clickOrder || 0),
    )
    newState[columnKey] = {
      order,
      clickOrder: maxClickOrder + 1,
    }
  }

  return newState
}

/**
 * 清除排序状态
 * @param sortState 当前排序状态
 * @param columnKey 要清除的列键，如果不提供则清除所有
 * @returns 更新后的排序状态
 */
export const clearSortState = (
  sortState: Record<string, SortState>,
  columnKey?: string,
): Record<string, SortState> => {
  if (columnKey) {
    const newState = { ...sortState }
    delete newState[columnKey]
    return newState
  }
  return {}
}

/**
 * 获取排序状态的显示信息
 * @param sortState 排序状态
 * @returns 排序显示信息数组
 */
export const getSortDisplayInfo = (sortState: Record<string, SortState>) => {
  return Object.entries(sortState)
    .map(([key, state]) => ({
      key,
      order: state.order,
      priority: state.multiple || state.clickOrder || 0,
    }))
    .sort((a, b) => b.priority - a.priority)
}

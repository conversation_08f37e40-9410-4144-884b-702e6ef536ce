import { useCommonStore } from '@/stores'
import type { SeriesData } from '@/types'
import {
  DailyReportService,
  type DailyReportQueryParams,
  type NewEnergyDataItem,
  type PeakingPeriodItem,
  type PeakingTimeDetailItem,
} from '@/utils/api/services/dailyReport'
import { ref } from 'vue'

export const useDailyReport = () => {
  const chartData = [
    {
      name: '实际市场出清',
      data: [],
    },
    {
      name: '超短期新能源预测',
      data: [],
    },
  ]

  const customConfig = {
    title: {
      left: -5,
      top: 7,
    },
    grid: {
      top: '25%',
    },
  }

  //调峰时段分析column 新能源类型  开始时间  结束时间 最大调峰电力
  const peakShavingColumns = [
    {
      key: 'type',
      title: '新能源类型',
      width: '21.5%',
    },
    {
      key: 'startTime',
      title: '开始时间',
      width: '18%',
    },
    {
      key: 'endTime',
      title: '结束时间',
      width: '18%',
    },
    {
      key: 'maxValue',
      title: '最大调峰电力（万千瓦）',
      align: 'center' as const,
    },
  ]

  const peakingTimeDetailColumns = [
    {
      key: 'stationName',
      title: '机组名称',
    },
    {
      key: 'value',
      title: '最大调峰电力（万千瓦）',
    },
  ]

  const peakingTimeDetailExportColumns = [
    {
      key: 'stationName',
      title: '机组名称',
    },
    {
      key: 'value',
      title: '最大调峰电力',
    },
  ]

  const description = ref<string>('')
  //调峰时段分析数据
  const peakShavingData = ref<PeakingPeriodItem[]>([])

  const peakingTimeDetailList = ref<PeakingTimeDetailItem[]>([])

  const statisticChartData = ref<SeriesData[]>([])

  const windChartData = ref<SeriesData[]>([])

  const solarChartData = ref<SeriesData[]>([])

  const generateChartData = (data: NewEnergyDataItem[]) => {
    const statisticData: SeriesData[] = []
    const windData: SeriesData[] = []
    const solarData: SeriesData[] = []

    chartData.forEach((item) => {
      statisticData.push({
        name: item.name,
        color: item.name === '实际市场出清' ? '#5E42CE' : '#EDAC00',
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: item.name === '实际市场出清' ? dataItem.totalPower : dataItem.totalPrediction,
        })),
      })

      windData.push({
        name: item.name,
        color: item.name === '实际市场出清' ? '#42CE96' : '#009CDE',
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: item.name === '实际市场出清' ? dataItem.windPower : dataItem.windPrediction,
        })),
      })

      solarData.push({
        name: item.name,
        color: item.name === '实际市场出清' ? '#72CE42' : '#DE00C0',
        data: data.map((dataItem) => ({
          name: dataItem.time,
          value: item.name === '实际市场出清' ? dataItem.pvPower : dataItem.pvPrediction,
        })),
      })
    })

    statisticChartData.value = statisticData
    windChartData.value = windData
    solarChartData.value = solarData
  }

  const commonStore = useCommonStore()

  const fetchDailyReportData = async (day: string) => {
    try {
      commonStore.loading = true
      const params: DailyReportQueryParams = {
        day,
      }
      const response = await DailyReportService.getDailyReportData(params)
      description.value = response.description + '。'
      peakShavingData.value = response.peakingPeriodList
      peakingTimeDetailList.value = response.peakingTimeDetailList
      generateChartData(response.newEnergyDataList)
      // 处理响应数据
    } catch (error) {
      console.error('获取日报数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  return {
    customConfig,
    peakingTimeDetailColumns,
    peakingTimeDetailExportColumns,
    peakingTimeDetailList,
    peakShavingColumns,

    description,
    peakShavingData,
    statisticChartData,
    windChartData,
    solarChartData,
    chartData,
    fetchDailyReportData,
  }
}

{"name": "spot-market-operation-quality-analysis", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.11.0", "dayjs": "^1.11.13", "docx": "^9.5.1", "echarts": "^5.6.0", "exceljs": "^4.4.0", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@unocss/eslint-config": "^66.3.3", "@vitejs/plugin-legacy": "^7.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-plugin-oxlint": "~1.9.0", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.5.1", "naive-ui": "^2.42.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.9.0", "prettier": "3.6.2", "rollup-plugin-visualizer": "^6.0.3", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^8.0.0", "vite-svg-loader": "^5.1.0", "vue-tsc": "^3.0.4"}}
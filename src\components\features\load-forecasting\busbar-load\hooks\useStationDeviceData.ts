import { useCommonStore } from '@/stores'
import { useBusbarLoadStore } from '@/stores/features/busbarLoad'
import { BusbarLoadService, type BusbarLoadDataDetailResponse } from '@/utils/api/'
import { formatTime } from '@/utils/tools'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

export const useStationDeviceData = () => {
  const route = useRoute()
  const commonStore = useCommonStore()
  const busbarLoadStore = useBusbarLoadStore()

  const stationDeviceData = ref<BusbarLoadDataDetailResponse>({
    busLfDataList: [],
    busLfStatisticList: [],
  })

  // 获取站点母线负荷统计信息
  const fetchBusbarLoadDataByStation = async (timeRange: [number, number]) => {
    commonStore.loading = true
    try {
      const response = await BusbarLoadService.getBusbarLoadDataByStation(
        {
          startDay: formatTime(timeRange[0], 'YYYY-MM-DD'),
          endDay: formatTime(timeRange[1], 'YYYY-MM-DD'),
          stationId: route.query.stationId as string,
        },
        busbarLoadStore.currentType,
      )
      stationDeviceData.value = response
    } catch (error) {
      console.error('获取全省母线负荷数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  // 获取设备母线负荷统计信息
  const fetchBusbarLoadDataByDevice = async (timeRange: [number, number]) => {
    commonStore.loading = true
    try {
      const response = await BusbarLoadService.getBusbarLoadDataByDevice(
        {
          startDay: formatTime(timeRange[0], 'YYYY-MM-DD'),
          endDay: formatTime(timeRange[1], 'YYYY-MM-DD'),
          deviceId: route.query.deviceId as string,
        },
        busbarLoadStore.currentType,
      )
      stationDeviceData.value = response
    } catch (error) {
      console.error('获取全省母线负荷数据失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  return {
    stationDeviceData,
    fetchBusbarLoadDataByStation,
    fetchBusbarLoadDataByDevice,
  }
}

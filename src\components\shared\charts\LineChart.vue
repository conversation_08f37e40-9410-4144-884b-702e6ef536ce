<template>
  <div>
    <div ref="chartRef" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { init, type ECharts } from 'echarts/core'
import type { LineChartProps } from '@/types/chart'
import { DEFAULT_GRADIENT_COLORS, DEFAULT_CHART_THEME } from '@/types/chart'
import {
  createGradientColor,
  generateGradientColors,
  getPureColor,
  mergeChartConfig,
} from '@/utils/chart'

const props = withDefaults(defineProps<LineChartProps>(), {
  title: '',
  width: '100%',
  height: '400px',
  showGrid: true,
  showLegend: true,
  smooth: true,
  yAxisName: '兆瓦',
  data: () => [],
})

const chartRef = ref<HTMLDivElement>()
let chartInstance: ECharts | null = null

// 计算X轴数据
const xAxisData = computed(() => {
  if (props.xAxisData?.length) {
    return props.xAxisData
  }
  return props.data[0]?.data.map((point) => point.name) || []
})

// 检查是否需要显示频率轴或差额轴
const hasFrequencyAxis = computed(() => props.data.some((item) => item.yAxisIndex === 1))

const hasDiffAxis = computed(() => props.data.some((item) => item.yAxisIndex === 2))

// 创建系列配置
const createSeriesConfig = () => {
  return props.data.map((item, index) => {
    // 生成渐变色配置
    const gradientColors =
      item.gradientColors ||
      generateGradientColors(item.color) ||
      DEFAULT_GRADIENT_COLORS[index % DEFAULT_GRADIENT_COLORS.length]

    // 默认系列配置
    const defaultSeriesConfig = {
      name: item.name,
      yAxisIndex: item.yAxisIndex || 0,
      type: 'line',
      smooth: props.smooth,
      data: item.data.map((point) => point.value),
      lineStyle: {
        width: 2,
        color: item.color || getPureColor(gradientColors.start),
      },
      areaStyle:
        item.isShowAreaStyle || item.gradientColors
          ? {
              color: createGradientColor(gradientColors),
            }
          : undefined,
      symbol: item.symbol || 'none',
      itemStyle: {
        color: item.color || getPureColor(gradientColors.start),
      },
    }

    // 合并自定义系列配置
    return defaultSeriesConfig
  })
}

// 创建Y轴配置
const createYAxisConfig = () => {
  const baseAxisConfig = {
    axisLine: {
      lineStyle: {
        color: DEFAULT_CHART_THEME.axisLineColor,
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#909399',
    },
    splitLine: {
      lineStyle: {
        color: DEFAULT_CHART_THEME.splitLineColor,
      },
    },
  }

  const yAxisConfig: any[] = [
    {
      type: 'value',
      name: props.yAxisName,
      nameTextStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
      },
      ...baseAxisConfig,
    },
    {
      type: 'value',
      show: hasFrequencyAxis.value,
      name: '赫兹',
      nameTextStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
      },
      ...baseAxisConfig,
    },
    {
      type: 'value',
      show: hasDiffAxis.value,
      name: props.yAxisName,
      nameTextStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
      },
      ...baseAxisConfig,
    },
  ]

  return yAxisConfig
}

// 获取图表配置
const getChartOption = () => {
  const series = createSeriesConfig()
  const yAxis = createYAxisConfig()

  // 默认配置
  const defaultOption = {
    title: {
      text: props.title,
      left: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: DEFAULT_CHART_THEME.gridColor,
      borderWidth: 1,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      show: props.showLegend,
      icon: 'rect',
      itemHeight: 2,
      top: hasFrequencyAxis.value || hasDiffAxis.value ? 0 : 5,
      right: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.showLegend ? '17%' : '5%',
      containLabel: true,
      borderColor: DEFAULT_CHART_THEME.gridColor,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData.value,
      axisLine: {
        lineStyle: {
          color: DEFAULT_CHART_THEME.axisLineColor,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#909399',
      },
    },
    yAxis,
    series,
  }

  // 使用智能合并配置
  return mergeChartConfig(defaultOption, props.customConfig)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = init(chartRef.value)
  chartInstance.setOption(getChartOption())
}

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(), true)
  }
}

const getImg = () => {
  if (chartInstance) {
    const width = chartInstance.getWidth()
    const height = chartInstance.getHeight()
    return {
      width,
      height,
      base64: chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      }),
    }
  }
  return {
    width: 0,
    height: 0,
    base64: '',
  }
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true },
)

// 监听其他配置变化
watch(
  [() => props.showGrid, () => props.showLegend, () => props.smooth, () => props.customConfig],
  () => {
    updateChart()
  },
  { deep: true },
)

// 处理窗口大小变化
const resize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initChart()
  window.addEventListener('resize', resize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resize)
})

// 暴露图表实例给父组件
defineExpose({
  chartInstance,
  updateChart,
  getImg,
  resize,
})
</script>

<style scoped></style>

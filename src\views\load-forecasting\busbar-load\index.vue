<template>
  <component :is="isProvince ? Province : City"></component>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import Province from '@/components/features/load-forecasting/busbar-load/province/index.vue'
import City from '@/components/features/load-forecasting/busbar-load/city/CityStatistic.vue'
import { useBusbarLoadStore } from '@/stores/features/busbarLoad'
import type { BusbarLoadType } from '@/utils/api/'

const route = useRoute()
const busbarLoadStore = useBusbarLoadStore()

const isProvince = computed(() => route.query.type === 'province' || !route.query.type)

// 根据路由名称确定类型
const busbarLoadType = computed<BusbarLoadType>(() => {
  return route.name === 'busbar-load-plan' ? 'plan' : 'spot'
})

// 监听路由变化，更新store中的类型
watch(
  busbarLoadType,
  (newType) => {
    busbarLoadStore.currentType = newType
  },
  { immediate: true },
)

onMounted(() => {
  // 初始化时设置类型
  busbarLoadStore.currentType = busbarLoadType.value
})
</script>

<style scoped></style>

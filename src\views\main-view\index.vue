<template>
  <n-flex>
    <n-layout position="absolute" has-sider>
      <Sidebar></Sidebar>
      <n-layout :native-scrollbar="false">
        <RouterView />

        <div
          v-if="commonStore.loading"
          class="flex items-center bg-white absolute left-0 top-0 right-0 bottom-0 justify-center bg-opacity-75"
        >
          <n-spin size="large" />
        </div>
      </n-layout>
    </n-layout>
  </n-flex>
</template>

<script setup lang="ts">
import { NLayout, NFlex, NSpin } from 'naive-ui'
import { RouterView } from 'vue-router'
import Sidebar from '@/components/layouts/sidebar/Sidebar.vue'

import { useMessage } from 'naive-ui'
import { useCommonStore } from '@/stores'

const commonStore = useCommonStore()

window.$message = useMessage()
</script>

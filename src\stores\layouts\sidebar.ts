import { shallowRef, computed, ref } from 'vue'
import { defineStore } from 'pinia'
import type { MenuOption } from 'naive-ui'
import type { BusbarLoadExistDeviceListResponse } from '@/utils/api/'
import { getCityRawOptions, menuRawOptions } from '@/utils/constant/options'

export const useSidebarStore = defineStore('sidebar', () => {
  // 使用 shallowRef 提升大型嵌套对象的性能
  const cityDataSpot = shallowRef<MenuOption[]>([])
  const cityDataPlan = shallowRef<MenuOption[]>([])
  const regionCodeMap = new Map<string, MenuOption>()
  const expandedKeys = ref<string[]>([])

  // 深度克隆菜单选项的辅助函数，处理包含函数的对象
  const deepCloneMenuOptions = (options: MenuOption[]): MenuOption[] => {
    return options.map((option) => ({
      ...option,
      children: option.children ? deepCloneMenuOptions(option.children) : undefined,
    }))
  }

  // 计算属性用于菜单选项，避免不必要的更新
  const menuOptions = computed(() => {
    const updatedMenu = deepCloneMenuOptions(menuRawOptions)
    const loadForecast = updatedMenu.find((item) => item.key === 'load-forecasting')
    const busbarLoadSpot = loadForecast?.children?.find((item) => item.key === 'busbar-load-spot')
    const busbarLoadPlan = loadForecast?.children?.find((item) => item.key === 'busbar-load-plan')

    if (busbarLoadPlan && cityDataPlan.value.length > 0) {
      busbarLoadPlan.children = cityDataPlan.value
    }
    if (busbarLoadSpot && cityDataSpot.value.length > 0) {
      busbarLoadSpot.children = cityDataSpot.value
    }

    return updatedMenu
  })

  const resetCityOptions = () => {
    // 使用浅克隆提升性能，label已在options中预渲染
    cityDataSpot.value = getCityRawOptions('spot').map((city) => ({ ...city }))
    cityDataPlan.value = getCityRawOptions('plan').map((city) => ({ ...city }))
    regionCodeMap.clear()

    // 更高效地构建区域代码映射
    cityDataSpot.value.forEach((city) => {
      if (city.regionCode) {
        regionCodeMap.set(city.regionCode as string, city)
      }
    })
  }

  const setCityOptions = (
    data: BusbarLoadExistDeviceListResponse[],
    targetRegionCode?: string,
    type: 'spot' | 'plan' = 'spot',
  ) => {
    // 选择要更新的城市数据
    const targetCityData = type === 'spot' ? cityDataSpot : cityDataPlan

    // 如果没有指定regionCode，则重置对应类型的城市数据
    if (!targetRegionCode) {
      targetCityData.value = getCityRawOptions(type).map((city) => ({ ...city }))

      // 如果是第一次初始化或者 regionCodeMap 为空，则重新构建映射
      if (regionCodeMap.size === 0) {
        regionCodeMap.clear()
        targetCityData.value.forEach((city) => {
          if (city.regionCode) {
            regionCodeMap.set(city.regionCode as string, city)
          }
        })
      }
    } else {
      // 如果指定了regionCode，确保regionCodeMap已初始化
      if (regionCodeMap.size === 0) {
        targetCityData.value.forEach((city) => {
          if (city.regionCode) {
            regionCodeMap.set(city.regionCode as string, city)
          }
        })
      }

      // 清空指定regionCode对应城市的children，保留原有的城市基础信息
      const targetCity = targetCityData.value.find((city) => city.regionCode === targetRegionCode)
      if (targetCity) {
        // 保留原有的城市基础选项，清空站点和设备数据
        const originalChildren =
          targetCity.children?.filter(
            (child) => child.type === 'city' || child.type === 'province',
          ) || []
        targetCity.children = originalChildren
      }
    }

    if (!data?.length) {
      // 数据为空时，保持当前状态
      return
    }

    // 预分配数组并使用批量操作
    const stationsToAdd = new Map<string, MenuOption[]>()

    // 批量处理数据以避免阻塞主线程
    data.forEach((item) => {
      const deviceChildren = item.deviceList.map((device) => ({
        label: device.name,
        key: device.id,
        deviceId: device.id,
        regionCode: device.regionCode,
        type: 'device',
        route: '/load-forecasting/busbar-load' + (type === 'plan' ? '-plan' : '-spot'),
      }))

      const station: MenuOption = {
        label: item.name,
        key: `${item.id}-${item.regionCode}`,
        regionCode: item.regionCode,
        children: [
          {
            label: item.name,
            key: item.id,
            stationId: item.id,
            regionCode: item.regionCode,
            type: 'station',
            route: '/load-forecasting/busbar-load' + (type === 'plan' ? '-plan' : '-spot'),
          },
          ...deviceChildren,
        ],
      }

      // 按区域代码分组站点以便批量插入
      if (!stationsToAdd.has(item.regionCode)) {
        stationsToAdd.set(item.regionCode, [])
      }
      stationsToAdd.get(item.regionCode)!.push(station)
    })

    // 批量更新城市数据
    stationsToAdd.forEach((stations, regionCode) => {
      // 如果指定了targetRegionCode，只更新对应的城市
      if (targetRegionCode && regionCode !== targetRegionCode) {
        return
      }

      const city = targetCityData.value.find((city) => city.regionCode === regionCode)
      if (city) {
        city.children = [...(city.children ?? []), ...stations]
      }
    })

    // 触发响应式更新
    targetCityData.value = [...targetCityData.value]
  }

  return {
    expandedKeys,
    menuOptions,
    setCityOptions,
    resetCityOptions,
  }
})

import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/main-view/index.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      redirect: '/section-monitoring',
      children: [
        {
          path: 'section-influence-peak-shaving',
          name: 'section-influence-peak-shaving',
          component: () => import('../views/section-influence/peak-shaving/index.vue'),
        },
        {
          path: 'section-monitoring',
          name: 'section-monitoring',
          component: () => import('../views/section-influence/section-monitoring/index.vue'),
        },
        {
          path: 'daily-report',
          name: 'daily-report',
          component: () => import('../views/daily-report/v1/index.vue'),
        },
        {
          path: 'daily-report-v2',
          name: 'daily-report-v2',
          component: () => import('../views/daily-report/v2/index.vue'),
        },
        {
          path: 'daily-report-v3',
          name: 'daily-report-v3',
          component: () => import('../views/daily-report/v3/index.vue'),
        },
        {
          path: 'load-forecasting',
          name: 'load-forecasting',
          redirect: '/load-forecasting/unified-load',
          component: () => import('../views/load-forecasting/index.vue'),
          children: [
            {
              path: 'unified-load',
              name: 'unified-load',
              component: () => import('../views/load-forecasting/unified-load/index.vue'),
            },
            {
              path: 'busbar-load-spot',
              name: 'busbar-load-spot',
              component: () => import('../views/load-forecasting/busbar-load/index.vue'),
            },
            {
              path: 'busbar-load-plan',
              name: 'busbar-load-plan',
              component: () => import('../views/load-forecasting/busbar-load/index.vue'),
            },
          ],
        },
        {
          path: 'cps-frequency',
          name: 'cps-frequency',
          component: () => import('../views/cps-frequency/index.vue'),
        },
      ],
    },
  ],
})

export default router

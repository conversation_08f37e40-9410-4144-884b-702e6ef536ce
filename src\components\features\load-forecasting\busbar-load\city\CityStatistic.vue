<template>
  <div class="flex flex-col h-full">
    <!-- 顶部搜索表单 -->
    <SearchForm @search="handleSearch">
      <template #form-fields>
        <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
          <n-date-picker
            v-model:value="busbarLoadStore.timeRange"
            class="w-full"
            size="large"
            type="daterange"
            placeholder="请选择时间"
            :is-date-disabled="disablePreviousDate"
            @update-value="handleDateChange"
          >
          </n-date-picker>
        </InputGroup>
        <InputGroup v-if="isCity" size="large" label="名称" class="mr-2.5 max-w-317px">
          <n-tree-select
            v-model:value="stationDevice"
            :options="stationDeviceOptions"
            size="large"
            filterable
            placeholder="请选择名称"
            clearable
            @update-value="handleStationDeviceChange"
          >
          </n-tree-select>
        </InputGroup>
      </template>

      <template #action-buttons>
        <ExportButton
          class="mr-2.5"
          :filename="filename"
          export-type="excel-structured"
          @export-start="handleExport"
          :structured-data="dataByExport"
        />
      </template>
    </SearchForm>
    <LineChart ref="chartRef" :data="chartData" yAxisName="万千瓦" height="450px" class="mt-2" />

    <n-scrollbar style="height: calc(100vh - 580px)">
      <div
        v-for="(item, index) in data.busLfStatisticList"
        :key="index"
        class="flex flex-col mb-5 pl-4"
      >
        <div class="flex">
          <LoadDetailCard :is-station-device="isGreenStyle" class="w-50" :data="item" />
          <div class="flex flex-col px-5">
            <!-- 横向表格数据 -->
            <VerticalTable
              class="flex-1"
              width="calc(100vw - 755px)"
              :columns="tableColumns"
              :data="item.busLfDataList"
            ></VerticalTable>
          </div>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, useTemplateRef, watchEffect } from 'vue'
import { NDatePicker, NTreeSelect, NScrollbar } from 'naive-ui'

import {
  disablePreviousDate,
  formatTime,
  isTimeRangeCrossDay,
  isTimeRangeExceed,
} from '@/utils/tools/'
import { useRoute, useRouter } from 'vue-router'
import {
  useCityData,
  useLoadDetailExport,
  useStationDeviceData,
  useStationDeviceList,
  useTreeSelect,
} from '../hooks'
import type { SeriesData, TableColumn } from '@/types'
import LineChart from '@/components/shared/charts/LineChart.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import LoadDetailCard from '@/components/features/load-forecasting/common/LoadDetailCard.vue'
import VerticalTable from '@/components/shared/tables/VerticalTable.vue'
import ExportButton from '@/components/shared/buttons/ExportButton.vue'

import { useBusbarLoadStore } from '@/stores'
import type { ExportBlockOptions } from '@/utils/export'

const router = useRouter()
const busbarLoadStore = useBusbarLoadStore()
const { getExportData } = useLoadDetailExport()

const route = useRoute()
const { handleStationDeviceChange } = useTreeSelect()

const stationDevice = ref('')

const searchTimeRange = ref<[number, number]>(busbarLoadStore.timeRange)

const tableColumns: TableColumn[] = [
  {
    key: 'time',
    title: '时间',
  },
  {
    key: 'sPrediction',
    title: '短期预测',
  },
  {
    key: 'usPrediction',
    title: '超短期预测',
  },
  {
    key: 'rtValue',
    title: '实时负荷',
  },
  {
    key: 'usDiff',
    title: '负荷偏差',
  },
  {
    key: 'usDiffRate',
    title: '负荷偏差率',
  },
]

const chartData = computed<SeriesData[]>(() => {
  if (!data.value.busLfDataList.length) return []

  return [
    {
      name: '负荷预测',
      color: 'rgba(206, 66, 174, 1)',
      isShowAreaStyle: true,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.usPrediction,
      })),
    },
    {
      name: '负荷实测',
      color: 'rgba(12, 163, 159, 1)',
      isShowAreaStyle: true,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.rtValue,
      })),
    },
    {
      name: '负荷偏差',
      color: '#004EE4',
      yAxisIndex: 2,
      data: data.value.busLfDataList.map((item) => ({
        name: item.time,
        value: item.usDiff,
      })),
    },
  ]
})

const { fetchStationDeviceList } = useStationDeviceList()

const handleDateChange = (value: [number, number]) => {
  const { type, regionCode } = route.query
  console.log('🚀 ~ handleDateChange ~ busbarLoadStore.currentType:', busbarLoadStore.currentType)

  // router.push({
  //   path: '/load-forecasting/busbar-load' + '-' + busbarLoadStore.currentType,
  //   query: {
  //     ...router.currentRoute.value.query,
  //   },
  // })

  // 如果是站或者设备页面，不进行数据请求
  if (type !== 'city') return

  fetchStationDeviceList(value, regionCode as string)
}

const { cityData, fetchBusbarLoadDataByRegion, fetchBusbarLoadDataByRegionMultiDay } = useCityData()

const { stationDeviceData, fetchBusbarLoadDataByStation, fetchBusbarLoadDataByDevice } =
  useStationDeviceData()

const data = computed(() => {
  if (route.query.stationId || route.query.deviceId) {
    return stationDeviceData.value
  } else {
    return cityData.value
  }
})

// 搜索处理函数
const handleSearch = () => {
  // 判断时间范围是否超过31天
  if (isTimeRangeExceed(busbarLoadStore.timeRange)) {
    window.$message.warning('时间范围不能超过31天')
    return
  }
  searchTimeRange.value = busbarLoadStore.timeRange
  // fetchData(route.query.stationId as string, route.query.deviceId as string)
}

const isMultiDay = computed(() => {
  return isTimeRangeCrossDay(searchTimeRange.value)
})

const fetchData = (stationId?: string, deviceId?: string) => {
  if (stationId) {
    fetchBusbarLoadDataByStation(searchTimeRange.value)
  } else if (deviceId) {
    fetchBusbarLoadDataByDevice(searchTimeRange.value)
  } else {
    if (isMultiDay.value) {
      fetchBusbarLoadDataByRegionMultiDay(searchTimeRange.value)
    } else {
      fetchBusbarLoadDataByRegion(searchTimeRange.value)
    }
  }
}

watchEffect(() => {
  const { stationId, deviceId } = route.query

  fetchData(stationId as string, deviceId as string)
})

// 使用watch监测stationId 和 deviceId的变化
// 如果在city页面，只修改时间，idChanged为false 不进行数据查询
// watch(
//   () => [route.query.stationId, route.query.deviceId, route.query.regionCode],
//   (newValues, oldValues) => {
//     const [newStationId, newDeviceId, newRegionCode] = newValues
//     const [oldStationId, oldDeviceId, oldRegionCode] = oldValues || []

//     const idChanged = newStationId !== oldStationId || newDeviceId !== oldDeviceId
//     const cityChanged = newRegionCode !== oldRegionCode
//     if (cityChanged && route.query.type === 'city') {
//       fetchData()
//     } else {
//       if (!idChanged) return // 如果都没变，不执行
//       fetchData(newStationId as string, newDeviceId as string)
//     }
//   },
//   { immediate: true },
// )

const isCity = computed(() => {
  return route.query.type === 'city'
})

const isGreenStyle = computed(() => {
  const { type } = route.query
  return type === 'station' || type === 'device' || !isMultiDay.value
})

const filename = computed(() => {
  return route.query.name + '母线负荷数据详情'
})

// 根据regionCode筛选busbarLoadStore中的stationDeviceList
const stationDeviceOptions = computed(() => {
  return busbarLoadStore.stationDeviceList.filter(
    (item) => item.regionCode === route.query.regionCode,
  )
})

const chartRef = useTemplateRef('chartRef')
const dataByExport = ref<ExportBlockOptions>()

const handleExport = () => {
  dataByExport.value = getExportData(
    data.value,
    chartRef.value,
    isMultiDay.value ? undefined : formatTime(searchTimeRange.value[0], 'YYYY-MM-DD'),
  )
}
</script>

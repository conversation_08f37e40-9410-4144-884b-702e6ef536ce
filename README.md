# 现货市场运行品质分析系统

> Spot Market Operation Quality Analysis System

## 目录

- [项目概述](#项目概述)
- [核心功能](#核心功能)
  - [CPS频率分析](#cps频率分析)
  - [负荷预测](#负荷预测)
  - [断面影响分析](#断面影响分析)
  - [日报](#日报)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [开发环境设置](#开发环境设置)
- [项目设置](#项目设置)
- [提交规范](#提交规范)

## 项目概述

现货市场运行品质分析系统是一个用于电力现货市场的运行品质分析与可视化平台。它旨在为电力调度和交易人员提供一个直观的Web界面，用于监控、分析和评估电力市场的实时运行状态和电能质量。

该系统完全基于前端技术实现，通过API获取后端数据进行展示和分析。

## 核心功能

系统包含以下几个核心功能模块：

### 📊 CPS频率分析

- 监控和分析电网的控制性能标准（CPS）和系统频率
- 提供如图表和统计数据等多种维度的可视化展示

### ⚡ 负荷预测

- 提供对不同颗粒度的电力负荷进行预测分析
- 包括母线负荷 (`busbar-load`) 和统一负荷 (`unified-load`)
- 展示预测曲线和实际负荷曲线的对比

### 📈 断面影响分析

- **断面监控**: 实时监控关键输电断面的潮流和功率
- **削峰填谷分析**: 分析电网的峰谷特性，评估调峰措施的效果

### 📋 日报

- 自动整合关键运行指标
- 生成和展示多种格式的日报

## 技术栈

| 技术类别 | 技术/工具 |
|---------|---------|
| **框架** | Vue 3 + TypeScript |
| **构建工具** | Vite |
| **状态管理** | Pinia |
| **路由** | Vue Router |
| **UI组件库** | Naive UI |
| **样式** | UnoCSS |
| **数据可视化** | ECharts |
| **包管理** | pnpm |

## 项目结构

```
src/
├── assets/           # 静态资源
├── components/       # 组件
│   ├── features/     # 功能组件
│   ├── layouts/      # 布局组件
│   └── shared/       # 共享组件
├── composables/      # 组合式函数
├── hooks/            # 自定义钩子
├── main.ts           # 应用入口
├── router/           # 路由配置
├── stores/           # 状态管理
├── types/            # 类型定义
├── utils/            # 工具函数
└── views/            # 页面视图
    ├── cps-frequency/    # CPS频率分析
    ├── daily-report/     # 日报
    ├── load-forecasting/ # 负荷预测
    ├── main-view/        # 主视图
    └── section-influence/ # 断面影响分析
```

## 开发环境设置

### 推荐的IDE配置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (并禁用 Vetur)

### TypeScript支持

TypeScript 默认无法处理 `.vue` 导入的类型信息，因此我们使用 `vue-tsc` 替代 `tsc` 进行类型检查。在编辑器中，我们需要 [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) 来使 TypeScript 语言服务识别 `.vue` 类型。

### 自定义配置

参考 [Vite 配置参考](https://vite.dev/config/)

## 项目设置

### 安装依赖

```sh
pnpm install
```

### 编译和热重载（开发环境）

```sh
pnpm dev
```

### 类型检查、编译和压缩（生产环境）

```sh
pnpm build
```

### 使用 ESLint 进行代码检查

```sh
pnpm lint
```

## 提交规范

提交类型：

- `feat`：新功能
- `fix`：修复 Bug
- `docs`：文档更新
- `style`：代码格式调整
- `refactor`：重构代码
- `test`：增加或修改测试
- `chore`：构建/工具相关变动（如配置更新）
- `perf`：性能优化
- `ci`：CI/CD 配置变动
- `build`：构建系统或依赖变动
